<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载织梦</title>
    <link rel="icon" type="image/x-icon" id="faviconIco">
    <link rel="icon" type="image/png" id="faviconPng">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Noto Sans SC', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                        },
                    },
                    backgroundImage: {
                        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        .animated-bg {
            background: #ffffff;
        }
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
            100% { transform: translateY(0px); }
        }
    </style>
</head>
<body class="animated-bg min-h-screen font-sans">
    <!-- 右上角代理信息 -->
    <div class="fixed top-16 right-8 z-50">
        <div class="px-6 py-3 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl shadow-sm border border-primary-100">
            <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <p class="font-medium text-gray-800">合作QQ: <span class="text-primary-600 font-semibold">1344553400</span></p>
            </div>
        </div>
    </div>

    <div class="relative z-10 mx-auto px-4 py-10">
        <!-- 英雄区域包含下载卡片 -->
        <div class="max-w-6xl mx-auto mb-20">
            <!-- 英雄信息 -->
            <div class="mb-16">
                <div>
                    <h1 class="text-5xl font-bold mb-6 text-gray-800 leading-tight">
                        支持<span class="text-secondary-600">Claude3.7</span>快速请求
                    </h1>
                    
                    <div class="space-y-4 mb-6 max-w-lg">
                        <div class="flex items-center space-x-3 text-gray-700">
                            <div class="bg-primary-500 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                </svg>
                            </div>
                            <span>支持cursor最新<span id="version">v0.49.x</span></span>
                        </div>
                        <div class="flex items-center space-x-3 text-gray-700">
                            <div class="bg-secondary-500 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                                </svg>
                            </div>
                            <span>无限额度,不限次数对话</span>
                        </div>
                        <div class="flex items-center space-x-3 text-gray-700">
                            <div class="bg-primary-500 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                                </svg>
                            </div>
                            <span>一键激活,告别繁琐等待</span>
                        </div>
                        <div class="flex items-center space-x-3 text-gray-700">
                            <div class="bg-secondary-500 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
                                </svg>
                            </div>
                            <span>全平台全版本完美支持</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 下载卡片区域，直接集成到英雄区域下方 -->
            <div id="download">
                <div class="text-center mb-6">
                    <div class="inline-block px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <span>下载前请先按 Ctrl+R 刷新页面获取最新内容</span>
                        </div>
                    </div>
                </div>
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Windows 下载卡片 -->
                    <div class="p-8 rounded-3xl hover:shadow-xl transition-all duration-500 border border-gray-200 shadow-md bg-white group flex flex-col items-center">
                        <div class="w-20 h-20 rounded-full bg-primary-500 flex items-center justify-center mb-6">
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M0,3.449L9.75,2.1V11.551H0ZM10.949,1.949L24,0V11.4H10.949ZM0,12.6H9.75V22.051L0,20.699ZM10.949,12.6H24V24L10.949,22.1Z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2 text-center">Windows 版本</h3>
                        <p class="text-gray-600 mb-6 text-center">支持 <span id="winVersion">Windows 7</span> 或以上版本</p>
                        <a id="winDownload" 
                           href="./Windows/CursorPro.zip"
                           class="mt-auto w-full bg-primary-500 text-white text-center py-3 px-6 rounded-xl font-medium transition-all duration-300 hover:bg-primary-600">
                            <div class="flex items-center justify-center space-x-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                <span>下载 Windows 版本</span>
                            </div>
                        </a>
                    </div>

                    <!-- macOS 下载卡片 -->
                    <div class="p-8 rounded-3xl hover:shadow-xl transition-all duration-500 border border-gray-200 shadow-md bg-white group flex flex-col items-center">
                        <div class="w-20 h-20 rounded-full bg-secondary-500 flex items-center justify-center mb-6">
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.71,19.5C17.88,20.74 17,21.95 15.66,21.97C14.32,22 13.89,21.18 12.37,21.18C10.84,21.18 10.37,21.95 9.1,22C7.79,22.05 6.8,20.68 5.96,19.47C4.25,17 2.94,12.45 4.7,9.39C5.57,7.87 7.13,6.91 8.82,6.88C10.1,6.86 11.32,7.75 12.11,7.75C12.89,7.75 14.37,6.68 15.92,6.84C16.57,6.87 18.39,7.1 19.56,8.82C19.47,8.88 17.39,10.1 17.41,12.63C17.44,15.65 20.06,16.66 20.09,16.67C20.06,16.74 19.67,18.11 18.71,19.5M13,3.5C13.73,2.67 14.94,2.04 15.94,2C16.07,3.17 15.6,4.35 14.9,5.19C14.21,6.04 13.07,6.7 11.95,6.61C11.8,5.46 12.36,4.26 13,3.5Z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2 text-center">macOS 版本</h3>
                        <p class="text-gray-600 mb-6 text-center">支持 <span id="macVersion">macOS 10.5</span> 或以上版本</p>
                        <a id="macDownload"
                           href="./Mac/CursorPro.zip"
                           class="mt-auto w-full bg-secondary-500 text-white text-center py-3 px-6 rounded-xl font-medium transition-all duration-300 hover:bg-secondary-600">
                            <div class="flex items-center justify-center space-x-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                <span>下载 macOS 版本</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- QQ交流群单独区域 -->
            <div class="mt-16 p-8 rounded-3xl hover:shadow-xl transition-all duration-500 border border-gray-200 shadow-md bg-white">
                <!-- 共用的宣传文字 -->
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">扫码加入交流群</h3>
                    <p class="text-gray-600 mb-4">扫码立即加入千人技术大群，解锁专属福利！</p>
                    <div class="flex flex-col space-y-2 mb-6 items-center">
                        <span class="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">🔑 下载即可免费使用</span>
                        <span class="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">💡 大神在线答疑解惑</span>
                        <span class="inline-block px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">🔥 最新技巧资源分享</span>
                    </div>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- 微信群 -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 flex flex-col items-center hover:shadow-xl transition-all duration-300">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">微信交流群</h3>
                        <div class="w-56 h-56 rounded-lg flex items-center justify-center overflow-hidden">
                            <img src="images/微信群.png" alt="微信交流群" class="w-52 h-52 object-cover">
                        </div>
                    </div>
                    
                    <!-- QQ群 -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 flex flex-col items-center hover:shadow-xl transition-all duration-300">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">QQ交流群</h3>
                        <div class="w-56 h-56 rounded-lg flex items-center justify-center overflow-hidden">
                            <img src="images/QQ群.jpg" alt="QQ交流群" class="w-52 h-52 object-cover">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部信息区域 -->
        <div class="text-center text-gray-500 mt-20">
        </div>
    </div>

    <!-- 悬浮装饰元素 -->
    <div class="fixed top-20 left-20 w-64 h-64 bg-primary-100 rounded-full blur-3xl -z-10"></div>
    <div class="fixed bottom-20 right-20 w-96 h-96 bg-secondary-100 rounded-full blur-3xl -z-10"></div>

    <!-- 将配置直接写在 HTML 中 -->
    <script>
        const downloadConfig = {
            appName: '织梦',
            appVersion: 'v0.49.x',
            favicon: {
                ico: './favicon.ico',
                png: './favicon.png'
            },
            logo: {
                path: './images/图标.png'
            },
            windows: {
                minVersion: 'Windows 7'
            },
            macos: {
                minVersion: 'macOS 10.5'
            }
        };

        // 等待 DOM 加载完成后执行
        document.addEventListener('DOMContentLoaded', () => {
            try {
                // 更新页面内容
                document.title = `下载${downloadConfig.appName}`;
                document.getElementById('version').textContent = downloadConfig.appVersion;
                
                // 更新 Windows 下载信息
                document.getElementById('winVersion').textContent = downloadConfig.windows.minVersion;
                
                // 更新 macOS 下载信息
                document.getElementById('macVersion').textContent = downloadConfig.macos.minVersion;

                // 更新 favicon
                document.getElementById('faviconIco').href = downloadConfig.favicon.ico;
                document.getElementById('faviconPng').href = downloadConfig.favicon.png;
                
                console.log('配置加载成功');
            } catch (error) {
                console.error('配置加载失败:', error);
            }
        });
    </script>

    <!-- 添加Alpine.js库，用于实现交互功能 -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</body>
</html>