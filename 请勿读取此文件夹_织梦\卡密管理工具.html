<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密批量生成工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #0066cc;
            margin-bottom: 20px;
            text-align: center;
        }
        .form-container {
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
        }
        .btn {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0052a3;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .copy-all {
            margin-top: 15px;
            text-align: center;
        }
        .card-list {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .text-center {
            text-align: center;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>卡密批量生成工具</h1>
        
        <div class="form-container">
            <div class="form-group">
                <label for="card-type">卡密类型</label>
                <select id="card-type">
                    <option value="时卡">时卡 (￥2.0)</option>
                    <option value="天卡">天卡 (￥6.6)</option>
                    <option value="周卡">周卡 (￥26.9)</option>
                    <option value="月卡">月卡 (￥49.9)</option>
                    <option value="年卡">年卡 (￥360.0)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="quantity">生成数量 (1-100)</label>
                <input type="number" id="quantity" min="1" max="100" value="1">
            </div>
            <div class="form-group">
                <label for="remark">备注</label>
                <input type="text" id="remark" value="批量生成" placeholder="卡密备注信息">
            </div>
            <div class="text-center">
                <button id="generate-btn" class="btn" onclick="generateCards()">生成卡密</button>
            </div>
        </div>
        
        <!-- 卡密到期时间查询区域 -->
        <div class="form-container" style="margin-bottom: 30px;">
            <div class="form-group">
                <label for="query-card-key">查询卡密到期时间</label>
                <input type="text" id="query-card-key" placeholder="请输入要查询的卡密">
            </div>
            <div class="text-center">
                <button class="btn" onclick="queryCardExpire()">查询到期</button>
            </div>
            <div id="query-result-status" class="status hidden"></div>
            <div id="query-card-result" class="hidden">
                <table style="margin-top:10px;">
                    <thead>
                        <tr>
                            <th>卡密</th>
                            <th>类型</th>
                            <th>到期时间</th>
                        </tr>
                    </thead>
                    <tbody id="query-card-table-body">
                        <!-- 查询结果填充 -->
                    </tbody>
                </table>
                
                <!-- 增加时长区域 -->
                <div style="margin-top: 20px; border-top: 1px solid #eee; padding-top: 15px;">
                    <h3>为该卡密增加时长</h3>
                    <div class="form-group">
                        <label for="add-duration-type">卡密类型</label>
                        <select id="add-duration-type">
                            <option value="时卡">时卡 (￥2.0)</option>
                            <option value="天卡">天卡 (￥6.6)</option>
                            <option value="周卡">周卡 (￥26.9)</option>
                            <option value="月卡">月卡 (￥49.9)</option>
                            <option value="年卡">年卡 (￥360.0)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add-duration-qty">增加数量</label>
                        <input type="number" id="add-duration-qty" min="1" value="1">
                    </div>
                    <div class="text-center">
                        <button class="btn" onclick="addCardDuration()">增加时长</button>
                    </div>
                    <div id="add-duration-status" class="status hidden"></div>
                </div>
            </div>
        </div>
        
        <div id="result-status" class="status hidden"></div>
        
        <div id="card-result" class="hidden">
            <h2>生成的卡密列表</h2>
            <div class="copy-all">
                <button class="btn" onclick="copyAllCards()">复制全部卡密</button>
                <button class="btn" onclick="exportToCSV()">导出为CSV</button>
            </div>
            <div class="card-list">
                <table>
                    <thead>
                        <tr>
                            <th>卡密</th>
                            <th>类型</th>
                            <th>到期时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="card-table-body">
                        <!-- 卡密数据将会动态添加到这里 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="form-group" style="margin-top: 20px;">
            <label for="all-cards">所有卡密（可直接复制）</label>
            <textarea id="all-cards" rows="10" readonly style="margin-top: 10px;"></textarea>
        </div>
    </div>

    <script>
        // 存储生成的卡密列表
        let cardList = [];
        
        // 设置服务器地址
        const serverUrl = window.location.origin;
        
        // 生成卡密
        function generateCards() {
            // 获取表单数据
            const cardType = document.getElementById('card-type').value;
            const quantity = parseInt(document.getElementById('quantity').value);
            const remark = document.getElementById('remark').value;
            
            // 验证数量
            if (isNaN(quantity) || quantity < 1 || quantity > 100) {
                alert('生成数量必须在1-100之间');
                return;
            }
            
            // 禁用生成按钮
            const generateBtn = document.getElementById('generate-btn');
            generateBtn.disabled = true;
            generateBtn.textContent = '正在生成...';
            
            // 隐藏之前的结果
            document.getElementById('card-result').classList.add('hidden');
            document.getElementById('all-cards').value = '';
            
            // 显示生成中状态
            const resultStatus = document.getElementById('result-status');
            resultStatus.textContent = '正在生成卡密，请稍候...';
            resultStatus.classList.remove('hidden', 'success', 'error');
            
            // 构建请求数据
            const requestData = {
                card_type: cardType,
                quantity: quantity,
                remark: remark
            };
            
            // 发送请求到服务器
            fetch(`${serverUrl}/api/admin/generate_cards`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`服务器响应错误：${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 显示成功信息
                    resultStatus.textContent = data.message;
                    resultStatus.classList.add('success');
                    
                    // 保存卡密列表
                    cardList = data.data;
                    
                    // 显示卡密列表
                    displayCards(cardList);
                } else {
                    resultStatus.textContent = `生成失败：${data.message}`;
                    resultStatus.classList.add('error');
                }
            })
            .catch(error => {
                resultStatus.textContent = `请求错误：${error.message}`;
                resultStatus.classList.add('error');
            })
            .finally(() => {
                // 恢复按钮状态
                generateBtn.disabled = false;
                generateBtn.textContent = '生成卡密';
            });
        }
        
        // 显示卡密列表
        function displayCards(cards) {
            if (!cards || !cards.length) {
                return;
            }
            
            // 清空表格内容
            const tableBody = document.getElementById('card-table-body');
            tableBody.innerHTML = '';
            
            // 添加卡密记录
            let allCardsText = '';
            
            cards.forEach(card => {
                // 添加到表格
                const row = document.createElement('tr');
                
                const cardCell = document.createElement('td');
                cardCell.textContent = card.卡密;
                row.appendChild(cardCell);
                
                const typeCell = document.createElement('td');
                typeCell.textContent = card.类型;
                row.appendChild(typeCell);
                
                const expireCell = document.createElement('td');
                // 格式化日期显示
                let expireText = '卡密未激活';
                if (card.到期时间) {
                    try {
                        // 尝试解析日期并格式化
                        const expireDate = new Date(card.到期时间);
                        if (!isNaN(expireDate.getTime())) {
                            expireText = expireDate.toISOString().replace('T', ' ').substring(0, 19);
                        } else {
                            expireText = card.到期时间;
                        }
                    } catch (e) {
                        expireText = card.到期时间 || '卡密未激活';
                    }
                }
                expireCell.textContent = expireText;
                row.appendChild(expireCell);
                
                const statusCell = document.createElement('td');
                statusCell.textContent = card.状态 || '未使用';
                row.appendChild(statusCell);
                
                tableBody.appendChild(row);
                
                // 添加到文本列表
                allCardsText += card.卡密 + '\n';
            });
            
            // 设置文本框内容
            document.getElementById('all-cards').value = allCardsText.trim();
            
            // 显示结果区域
            document.getElementById('card-result').classList.remove('hidden');
        }
        
        // 复制所有卡密
        function copyAllCards() {
            const textarea = document.getElementById('all-cards');
            if (!textarea.value) {
                alert('没有可复制的卡密');
                return;
            }
            
            textarea.select();
            document.execCommand('copy');
            
            alert(`已复制 ${cardList.length} 个卡密到剪贴板`);
        }
        
        // 导出为CSV文件
        function exportToCSV() {
            if (!cardList || !cardList.length) {
                alert('没有可导出的卡密');
                return;
            }
            
            // 创建CSV内容
            let csvContent = "卡密,类型,到期时间,状态\n";
            
            cardList.forEach(card => {
                // 格式化日期
                let expireText = '卡密未激活';
                if (card.到期时间) {
                    try {
                        const expireDate = new Date(card.到期时间);
                        if (!isNaN(expireDate.getTime())) {
                            expireText = expireDate.toISOString().replace('T', ' ').substring(0, 19);
                        } else {
                            expireText = card.到期时间;
                        }
                    } catch (e) {
                        expireText = card.到期时间 || '卡密未激活';
                    }
                }
                
                csvContent += `${card.卡密},${card.类型},${expireText},${card.状态 || '未使用'}\n`;
            });
            
            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", `卡密列表_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.csv`);
            link.style.visibility = 'hidden';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // 查询卡密到期时间
        function queryCardExpire() {
            const cardKey = document.getElementById('query-card-key').value.trim();
            const resultStatus = document.getElementById('query-result-status');
            const resultDiv = document.getElementById('query-card-result');
            const tableBody = document.getElementById('query-card-table-body');
            
            // 清空之前的结果
            resultStatus.className = 'status hidden';
            resultStatus.textContent = '';
            resultDiv.classList.add('hidden');
            tableBody.innerHTML = '';
            
            // 清空增加时长状态
            document.getElementById('add-duration-status').className = 'status hidden';
            document.getElementById('add-duration-status').textContent = '';
            
            if (!cardKey) {
                resultStatus.textContent = '请输入要查询的卡密';
                resultStatus.className = 'status error';
                resultStatus.classList.remove('hidden');
                return;
            }
            
            resultStatus.textContent = '正在查询...';
            resultStatus.className = 'status';
            resultStatus.classList.remove('hidden', 'success', 'error');
            
            fetch(`${serverUrl}/api/admin/search_cards`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ card_key: cardKey })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`服务器响应错误：${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.data && data.data.length > 0) {
                    const card = data.data[0];
                    
                    // 自动设置类型下拉框
                    if (card.类型) {
                        document.getElementById('add-duration-type').value = card.类型;
                    }
                    
                    // 填充表格
                    const row = document.createElement('tr');
                    const cardCell = document.createElement('td');
                    cardCell.textContent = card.卡密;
                    row.appendChild(cardCell);
                    const typeCell = document.createElement('td');
                    typeCell.textContent = card.类型;
                    row.appendChild(typeCell);
                    const expireCell = document.createElement('td');
                    
                    // 格式化日期显示
                    let expireText = '卡密未激活';
                    if (card.到期时间) {
                        try {
                            // 尝试解析日期并格式化
                            const expireDate = new Date(card.到期时间);
                            if (!isNaN(expireDate.getTime())) {
                                expireText = expireDate.toISOString().replace('T', ' ').substring(0, 19);
                            } else {
                                expireText = card.到期时间;
                            }
                        } catch (e) {
                            expireText = card.到期时间 || '卡密未激活';
                        }
                    }
                    expireCell.textContent = expireText;
                    
                    row.appendChild(expireCell);
                    tableBody.appendChild(row);
                    resultDiv.classList.remove('hidden');
                    resultStatus.textContent = '查询成功';
                    resultStatus.className = 'status success';
                } else {
                    resultStatus.textContent = data.message || '未找到该卡密';
                    resultStatus.className = 'status error';
                }
            })
            .catch(error => {
                resultStatus.textContent = `请求错误：${error.message}`;
                resultStatus.className = 'status error';
            });
        }
        
        // 增加卡密时长
        function addCardDuration() {
            const cardKey = document.getElementById('query-card-key').value.trim();
            const cardType = document.getElementById('add-duration-type').value;
            const quantity = parseInt(document.getElementById('add-duration-qty').value);
            const statusDiv = document.getElementById('add-duration-status');
            
            // 清空状态
            statusDiv.className = 'status hidden';
            statusDiv.textContent = '';
            
            // 验证输入
            if (!cardKey) {
                statusDiv.textContent = '请先输入卡密并查询';
                statusDiv.className = 'status error';
                statusDiv.classList.remove('hidden');
                return;
            }
            
            if (isNaN(quantity) || quantity < 1) {
                statusDiv.textContent = '数量必须大于0';
                statusDiv.className = 'status error';
                statusDiv.classList.remove('hidden');
                return;
            }
            
            // 显示处理中状态
            statusDiv.textContent = '正在增加时长...';
            statusDiv.className = 'status';
            statusDiv.classList.remove('hidden', 'success', 'error');
            
            // 发送请求
            fetch(`${serverUrl}/api/admin/add_card_duration`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    card_key: cardKey,
                    card_type: cardType,
                    quantity: quantity
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`服务器响应错误：${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    statusDiv.textContent = `增加时长成功！已为卡密 ${cardKey} 增加 ${quantity} 个${cardType}`;
                    statusDiv.className = 'status success';
                    
                    // 自动刷新查询结果
                    setTimeout(() => {
                        queryCardExpire();
                    }, 1500);
                } else {
                    statusDiv.textContent = data.message || '增加时长失败';
                    statusDiv.className = 'status error';
                }
            })
            .catch(error => {
                statusDiv.textContent = `请求错误：${error.message}`;
                statusDiv.className = 'status error';
            });
        }
    </script>
</body>
</html> 