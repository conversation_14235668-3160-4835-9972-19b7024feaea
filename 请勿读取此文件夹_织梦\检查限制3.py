#此脚本在服务端运行，用于检查限制
import mysql.connector
import datetime
import logging
import time
import functools
import os
from logging.handlers import RotatingFileHandler
from flask import Flask, request, jsonify
from mysql.connector import pooling  # 添加连接池模块导入
from dataclasses import dataclass  # 添加dataclass导入
import requests
import hashlib
import base64
import struct

# 配置日志
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
# 使用RotatingFileHandler进行日志轮转
file_handler = RotatingFileHandler(
    os.path.join(log_dir, 'app.log'), 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
console_handler = logging.StreamHandler()

# 生产环境中使用INFO级别，减少日志量
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[file_handler, console_handler]
)

# 设置Flask的日志级别
logging.getLogger('werkzeug').setLevel(logging.INFO)

class Config:
    """系统配置类"""
    # 数据库配置
    数据库配置 = {
        'host': 'localhost',
        # 'host': '***************',
        'port': 3306,
        'user': 'root',
        'password': 'YU6709',
        'database': 'kami3162',
        # 连接池配置
        'pool_name': 'mysql_pool',
        'pool_size': 32,  # 连接池大小，MySQL Connector限制最大为32
        'pool_reset_session': True,  # 重置会话状态
        'autocommit': True,  # 自动提交事务
        # 连接有效性检查配置
        'connection_timeout': 10,  # 连接超时时间(秒)
        'use_pure': True,  # 使用纯Python实现
        'get_warnings': True,  # 获取警告信息
    }
    
    # 查询超时配置
    查询超时 = 5  # 秒
    
    # 缓存配置
    启用缓存 = True
    缓存过期时间 = 300  # 秒

    # 试用账号配置
    试用账号配置 = {
        '最大获取次数': 3,  # 最大试用次数
        '获取频率限制': 3,  # 获取频率限制（秒）
        '每IP最大设备数': 1,  # 每个IP允许激活的最大设备数量
        '启用IP限制': True,   # 是否启用IP限制功能
    }
    
    # 付费账号配置
    付费账号配置 = {
        '十分钟最大调用次数': 3,  # 每10分钟最大调用次数
        '二十分钟最大调用次数': 6,  # 每20分钟最大调用次数
        '默认最大机器码数量': 1,  # 默认最大机器码数量
        '获取频率限制': 3,  # 获取频率限制（秒）
        '换绑冷却时间': 5,  # 换绑冷却时间（分钟），默认5分钟
        '启用机器码检测': False,  # 是否启用机器码不同检测，默认False，允许不同机器码登录
    }
    
    # 邮箱账号配置
    邮箱账号配置 = {
        '免费账号最大人数': 1,  # 免费账号最大使用人数
        '付费账号最大人数': 1,  # 付费账号最大使用人数
    }
    
    # API服务配置
    API服务配置 = {
        '监听地址': '0.0.0.0',  # 监听地址，0.0.0.0表示所有地址
        '端口': 5267,  # 监听端口
        '调试模式': False,  # 是否开启调试模式
    }

# 简单的内存缓存实现
class 缓存管理器:
    """缓存管理器，用于缓存查询结果"""
    def __init__(self):
        self.缓存 = {}
        self.过期时间 = {}
    
    def 获取(self, 键):
        """获取缓存"""
        if not Config.启用缓存:
            return None
        
        当前时间 = time.time()
        if 键 in self.缓存 and 当前时间 < self.过期时间.get(键, 0):
            logging.debug(f"缓存命中: {键}")
            return self.缓存[键]
        
        if 键 in self.缓存:
            # 缓存过期，清除
            del self.缓存[键]
            if 键 in self.过期时间:
                del self.过期时间[键]
        
        return None
    
    def 设置(self, 键, 值, 过期秒数=None):
        """设置缓存"""
        if not Config.启用缓存:
            return
        
        过期秒数 = 过期秒数 or Config.缓存过期时间
        self.缓存[键] = 值
        self.过期时间[键] = time.time() + 过期秒数
        logging.debug(f"缓存设置: {键}, 过期时间: {过期秒数}秒")
    
    def 清除(self, 键=None):
        """清除缓存"""
        if 键 is None:
            self.缓存.clear()
            self.过期时间.clear()
            logging.debug("清除所有缓存")
        elif 键 in self.缓存:
            del self.缓存[键]
            if 键 in self.过期时间:
                del self.过期时间[键]
            logging.debug(f"清除缓存: {键}")
    
    def 清理过期缓存(self):
        """清理所有过期缓存"""
        当前时间 = time.time()
        过期键列表 = [键 for 键, 过期时间 in self.过期时间.items() if 当前时间 >= 过期时间]
        
        for 键 in 过期键列表:
            del self.缓存[键]
            del self.过期时间[键]
        
        if 过期键列表:
            logging.debug(f"清理过期缓存: {len(过期键列表)}项")

# 全局缓存实例
缓存 = 缓存管理器()

class 连接池管理器:
    """数据库连接池管理"""
    _连接池实例 = None
    
    def __init__(self, 配置=None):
        self.数据库配置 = 配置 or Config.数据库配置
        
        if 连接池管理器._连接池实例 is None:
            try:
                # 创建连接池
                连接池管理器._连接池实例 = pooling.MySQLConnectionPool(
                    **self.数据库配置
                )
                logging.info(f"数据库连接池初始化成功，池大小: {self.数据库配置.get('pool_size', 32)}")
            except Exception as e:
                logging.error(f"数据库连接池初始化失败: {e}", exc_info=True)
                raise
    
    def 获取连接(self):
        """从连接池获取一个连接"""
        重试次数 = 3
        当前尝试 = 0
        
        while 当前尝试 < 重试次数:
            try:
                conn = 连接池管理器._连接池实例.get_connection()
                
                # 检查连接是否有效
                try:
                    conn.ping(reconnect=True, attempts=1, delay=0)
                except:
                    # 连接无效，关闭并重试
                    conn.close()
                    raise
                    
                return conn
            except Exception as e:
                当前尝试 += 1
                if 当前尝试 >= 重试次数:
                    logging.error(f"从连接池获取连接失败: {e}")
                    raise
                logging.warning(f"连接获取失败，正在重试 ({当前尝试}/{重试次数}): {e}")
                time.sleep(0.5)  # 短暂延迟后重试
    
    def 执行查询(self, sql, 参数=None, 缓存键=None, 缓存时间=None):
        """
        执行查询并返回结果，支持查询缓存和超时控制
        :param sql: SQL语句
        :param 参数: SQL参数
        :param 缓存键: 缓存键，如果提供则启用缓存
        :param 缓存时间: 缓存过期时间(秒)
        :return: 查询结果
        """
        # 检查缓存
        if 缓存键 and Config.启用缓存:
            缓存结果 = 缓存.获取(缓存键)
            if 缓存结果 is not None:
                return 缓存结果
        
        conn = None
        try:
            conn = self.获取连接()
            
            # 设置查询超时
            cursor = conn.cursor(dictionary=True)
            
            # 记录开始时间
            开始时间 = time.time()
            
            cursor.execute(sql, 参数 or ())
            
            # 检查是否超时
            if time.time() - 开始时间 > Config.查询超时:
                logging.warning(f"查询执行时间过长: {time.time() - 开始时间:.2f}秒, SQL: {sql}")
            
            result = cursor.fetchall()
            cursor.close()
            
            # 设置缓存
            if 缓存键 and Config.启用缓存:
                缓存.设置(缓存键, result, 缓存时间)
            
            return result
        except Exception as e:
            logging.error(f"查询执行失败: {e}, SQL: {sql}")
            raise
        finally:
            if conn:
                conn.close()  # 这里不是真正关闭连接，而是归还到连接池
    
    def 执行单条查询(self, sql, 参数=None, 缓存键=None, 缓存时间=None):
        """
        执行查询并返回单条结果，支持查询缓存和超时控制
        :param sql: SQL语句
        :param 参数: SQL参数
        :param 缓存键: 缓存键，如果提供则启用缓存
        :param 缓存时间: 缓存过期时间(秒)
        :return: 查询结果
        """
        # 检查缓存
        if 缓存键 and Config.启用缓存:
            缓存结果 = 缓存.获取(缓存键)
            if 缓存结果 is not None:
                return 缓存结果
        
        conn = None
        try:
            conn = self.获取连接()
            
            # 设置查询超时
            cursor = conn.cursor(dictionary=True)
            
            # 记录开始时间
            开始时间 = time.time()
            
            cursor.execute(sql, 参数 or ())
            
            # 检查是否超时
            if time.time() - 开始时间 > Config.查询超时:
                logging.warning(f"查询执行时间过长: {time.time() - 开始时间:.2f}秒, SQL: {sql}")
            
            result = cursor.fetchone()
            cursor.close()
            
            # 设置缓存
            if 缓存键 and Config.启用缓存:
                缓存.设置(缓存键, result, 缓存时间)
            
            return result
        except Exception as e:
            logging.error(f"单条查询执行失败: {e}, SQL: {sql}")
            raise
        finally:
            if conn:
                conn.close()  # 归还到连接池
    
    def 执行更新(self, sql, 参数=None, 清除缓存键=None):
        """
        执行更新操作，支持清除相关缓存
        :param sql: SQL语句
        :param 参数: SQL参数
        :param 清除缓存键: 需要清除的缓存键或键列表
        :return: 影响的行数
        """
        conn = None
        try:
            conn = self.获取连接()
            cursor = conn.cursor()
            
            # 记录开始时间
            开始时间 = time.time()
            
            cursor.execute(sql, 参数 or ())
            
            # 检查是否超时
            if time.time() - 开始时间 > Config.查询超时:
                logging.warning(f"更新执行时间过长: {time.time() - 开始时间:.2f}秒, SQL: {sql}")
            
            conn.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            
            # 清除相关缓存
            if 清除缓存键:
                if isinstance(清除缓存键, list):
                    for 键 in 清除缓存键:
                        缓存.清除(键)
                else:
                    缓存.清除(清除缓存键)
            
            return affected_rows
        except Exception as e:
            if conn:
                conn.rollback()  # 出错时回滚
            logging.error(f"更新执行失败: {e}, SQL: {sql}")
            raise
        finally:
            if conn:
                conn.close()  # 归还到连接池

class 数据库管理器(连接池管理器):
    """数据库连接和操作管理，使用连接池优化性能"""
    def __init__(self, 配置=None):
        super().__init__(配置)

class 账号管理器:
    """统一的账号管理器，整合了免费和付费账号功能"""
    # 添加类变量用于缓存可用账号
    可用账号缓存 = []
    上次检测时间 = None
    
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
    
    def 检查系统配置(self):
        """检查全局系统配置"""
        try:
            配置 = self.数据库.执行单条查询(
                "SELECT 开启免费号池, 开启付费号池, 网络版本号_Windows, 网络版本号_Mac, 月卡, 年卡 FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            if 配置:
                return 配置 # 返回字典格式
            
            # 返回默认值
            return {
                '开启免费号池': 0,
                '开启付费号池': 0,
                '网络版本号_Windows': '',
                '网络版本号_Mac': '',
                '月卡': None, # 根据实际字段类型设置默认值
                '年卡': None  # 根据实际字段类型设置默认值
            }
        except Exception as e:
            logging.error(f"检查系统配置失败: {e}")
            # 出错时也返回默认值字典
            return {
                '开启免费号池': 0,
                '开启付费号池': 0,
                '网络版本号_Windows': '',
                '网络版本号_Mac': '',
                '月卡': None,
                '年卡': None
            }
    
    def 检查获取频率(self, 标识, 是卡密=False):
        """检查获取频率限制"""
        try:
            # 选择表名和字段名
            表名 = "卡密系统" if 是卡密 else "试用账号"
            字段名 = "卡密" if 是卡密 else "机器码"
            
            记录 = self.数据库.执行单条查询(
                f"SELECT 上次获取时间 FROM {表名} WHERE {字段名} = %s",
                (标识,)
            )
            
            当前时间 = datetime.datetime.now()
            
            if 记录 and 记录.get('上次获取时间'):
                上次获取时间 = 记录.get('上次获取时间')
                
                # 处理未来时间
                if 上次获取时间 > 当前时间:
                    上次获取时间 = 当前时间
                
                # 计算时间差（秒）
                时间差 = (当前时间 - 上次获取时间).total_seconds()
                
                # 根据是否是卡密用户选择不同的获取频率限制
                获取频率限制 = Config.付费账号配置['获取频率限制'] if 是卡密 else Config.试用账号配置['获取频率限制']
                
                # 如果时间差小于配置的限制时间，拒绝请求
                if 时间差 < 获取频率限制:
                    剩余时间 = 获取频率限制 - 时间差
                    剩余分钟 = int(剩余时间 // 60)
                    剩余秒 = int(剩余时间 % 60)
                    return False, f"您的额度还未使用完!无需再次获取，冷却{剩余分钟}分{剩余秒}秒"
            
            return True, None
            
        except Exception as e:
            logging.error(f"检查获取频率失败: {e}")
            return True, None
    
    def 更新获取时间(self, 标识, 是卡密=False):
        """更新上次获取时间"""
        try:
            表名 = "卡密系统" if 是卡密 else "试用账号"
            字段名 = "卡密" if 是卡密 else "机器码"
            
            self.数据库.执行更新(
                f"UPDATE {表名} SET 上次获取时间 = %s WHERE {字段名} = %s",
                (datetime.datetime.now(), 标识)
            )
            
        except Exception as e:
            logging.error(f"更新获取时间失败: {e}")
    
    
    def 检查账号授权(self, token):
        """检查账号是否授权"""
        try:
            # 清理token
            if token and '%3A%3A' in token:
                token = token.split('%3A%3A')[1]
            elif token and '::' in token:
                token = token.split('::')[1]
            
            token = token.strip()
            
            if not token or len(token) < 10:
                return False, "无效令牌"

            # 生成校验和
            try:
                # 生成machineId和macMachineId
                machine_id = hashlib.sha256((token + 'machineId').encode('utf-8')).hexdigest()
                mac_machine_id = hashlib.sha256((token + 'macMachineId').encode('utf-8')).hexdigest()
                
                # 获取时间戳并转换为字节数组
                timestamp = int(time.time() * 1000) // 1000000
                byte_array = bytearray(struct.pack('>Q', timestamp)[-6:])
                
                # 混淆字节
                t = 165
                for r in range(len(byte_array)):
                    byte_array[r] = ((byte_array[r] ^ t) + (r % 256)) & 0xFF
                    t = byte_array[r]
                
                # 编码为base64
                encoded_checksum = base64.b64encode(byte_array).decode('utf-8')
                checksum = f"{encoded_checksum}{machine_id}/{mac_machine_id}"
            except Exception as e:
                logging.error(f"生成校验和失败: {str(e)}")
                checksum = ""
            
            # 创建请求头
            headers = {
                'accept-encoding': 'gzip',
                'authorization': f'Bearer {token}',
                'connect-protocol-version': '1',
                'content-type': 'application/proto',
                'user-agent': 'connect-es/1.6.1',
                'x-cursor-checksum': checksum,
                'x-cursor-client-version': '0.48.7',
                'x-cursor-timezone': 'Asia/Shanghai',
                'x-ghost-mode': 'false',
                'Host': 'api2.cursor.sh'
            }
            
            # 发送请求
            response = requests.post(
                'https://api2.cursor.sh/aiserver.v1.DashboardService/GetUsageBasedPremiumRequests',
                headers=headers,
                data=b'',
                timeout=10
            )
            
            if response.status_code == 200:
                return True, "正常"
            elif response.status_code in [401, 403]:
                return False, "已受限"
            else:
                if token.startswith('eyJ') and '.' in token and len(token) > 100:
                    return True, "可能正常"
                return False, f"状态码异常({response.status_code})"
                
        except Exception as e:
            if token.startswith('eyJ') and '.' in token and len(token) > 100:
                return True, "可能正常"
            return False, f"检查错误: {str(e)}"


    def 删除异常账号(self, 邮箱, 访问令牌, 刷新令牌, 原因, 用户标识=None):
        """删除异常账号并记录到已删除邮箱表"""
        try:
            # 使用INSERT IGNORE忽略唯一键冲突
            self.数据库.执行更新(
                """
                INSERT IGNORE INTO 已删除邮箱(邮箱, 访问令牌, 刷新令牌, 创建时间, 用户标识, 删除原因)
                VALUES (%s, %s, %s, NOW(), %s, %s)
                """,
                (邮箱, 访问令牌, 刷新令牌, 用户标识, 原因)
            )
            
            # 从邮箱系统表中删除
            self.数据库.执行更新(
                "DELETE FROM 邮箱系统 WHERE 邮箱 = %s",
                (邮箱,)
            )
            
            logging.info(f"已删除异常账号: {邮箱[:5]}*** 原因: {原因} 用户: {用户标识 or '未知'}")
            
        except Exception as e:
            logging.error(f"删除异常账号失败: {str(e)[:100]}")


    def 获取账号(self, 用户标识, 是卡密=False):
        """获取账号，直接从可用账号缓存中获取
        如果缓存为空，会尝试补充缓存
        """
        try:
            # 检查获取频率
            频率检查结果, 频率错误消息 = self.检查获取频率(用户标识, 是卡密)
            if not 频率检查结果:
                logging.info(f"用户 {用户标识} 获取频率限制: {频率错误消息}")
                return False, None, None, None, 频率错误消息
            
            # 如果缓存为空，尝试补充账号
            if not 账号管理器.可用账号缓存:
                logging.warning("缓存中无可用账号，执行账号检测")
                self.维持10个可用账号()
                
                # 如果补充后仍然为空，返回错误
                if not 账号管理器.可用账号缓存:
                    错误消息 = '当前付费号池资源紧张,请稍后再试' if 是卡密 else '当前免费号池资源紧张,如需立即使用请升级会员'
                    logging.error("补充缓存失败，无可用账号")
                    return False, None, None, None, 错误消息
            
            # 从缓存中获取一个账号
            缓存账号 = 账号管理器.可用账号缓存.pop(0)
            邮箱 = 缓存账号['邮箱']
            访问令牌 = 缓存账号['访问令牌'] 
            刷新令牌 = 缓存账号['刷新令牌']
            
            表名 = "邮箱系统"
            人数上限 = Config.邮箱账号配置['付费账号最大人数'] if 是卡密 else Config.邮箱账号配置['免费账号最大人数']
            
            try:
                # 查询当前人数
                邮箱信息 = self.数据库.执行单条查询(
                    f"SELECT IFNULL(人数, 0) as 人数 FROM {表名} WHERE 邮箱 = %s",
                    (邮箱,)
                )
                
                # 如果邮箱不存在于表中，可能已被删除
                if not 邮箱信息:
                    logging.warning(f"账号 {邮箱} 不存在于数据库中，可能已被删除")
                    # 如果缓存不为空，递归调用自身获取下一个账号
                    if 账号管理器.可用账号缓存:
                        return self.获取账号(用户标识, 是卡密)
                    else:
                        return False, None, None, None, "缓存账号已失效，请稍后再试"
                
                当前人数 = int(邮箱信息.get('人数', 0))
                新人数 = 当前人数 + 1
                
                # 如果超过人数上限，则删除账号但仍然使用
                if 新人数 >= 人数上限:
                    logging.info(f"邮箱 {邮箱} 人数({新人数})超过上限({人数上限})，标记删除")
                    self.删除异常账号(邮箱, 访问令牌, 刷新令牌, "超过人数上限", 用户标识)
                else:
                    # 更新人数
                    self.数据库.执行更新(
                        f"UPDATE {表名} SET 人数 = %s WHERE 邮箱 = %s",
                        (新人数, 邮箱)
                    )
            except Exception as e:
                logging.error(f"更新邮箱 {邮箱} 使用情况时出错: {e}")
                # 即使更新出错，仍然返回账号
            
            # 更新获取时间
            self.更新获取时间(用户标识, 是卡密)
            
            # 返回真实账号
            # logging.info(f"用户 {用户标识} 成功获取账号: {邮箱}")
            # return True, 邮箱, 访问令牌, 刷新令牌, None
                
            # 生成虚假邮箱，但保留原始账号的处理逻辑
            import random
            import string
            字符集 = string.ascii_letters + string.digits
            随机字符串 = ''.join(random.choice(字符集) for _ in range(8))
            虚假邮箱 = f"zhimeng{随机字符串}@gmail.com"
            logging.info(f"用户 {用户标识} 成功获取账号: {邮箱}，将返回虚假账号: {虚假邮箱}")
            return True, 虚假邮箱, 访问令牌, 刷新令牌, None
        
        except Exception as e:
            logging.error(f"获取邮箱账号出错: {e}", exc_info=True)
            return False, None, None, None, f"系统错误: {e}"

    # === 试用账号相关功能 ===
    
    def 检查IP限制(self, ip):
        """检查IP是否超过限制"""
        if not Config.试用账号配置['启用IP限制']:
            return True, "IP限制未启用"

        try:
            # 先查询IP是否已存在
            ip_record = self.数据库.执行单条查询(
                "SELECT * FROM 试用账号IP WHERE 试用IP=%s",
                (ip,)
            )

            if ip_record:
                # 检查IP是否被禁用
                if ip_record['IP是否被禁用'] != 0:
                    return False, "IP已被禁用"

                # 检查设备数量是否超过限制
                if ip_record['设备数量'] > Config.试用账号配置['每IP最大设备数']:
                    logging.info(f"IP已达到最大设备数量限制: {Config.试用账号配置['每IP最大设备数']}")
                    return False, "您的免费额度已用完,如需立即使用请升级会员"

                # 更新最近登录时间
                self.数据库.执行更新(
                    "UPDATE 试用账号IP SET 最近登录时间=%s WHERE 试用IP=%s",
                    (datetime.datetime.now(), ip)
                )
            else:
                # 创建IP记录
                self.数据库.执行更新(
                    "INSERT INTO 试用账号IP(试用IP, 设备数量, IP是否被禁用, 首次登录时间, 最近登录时间) VALUES(%s, 0, 0, %s, %s)",
                    (ip, datetime.datetime.now(), datetime.datetime.now())
                )

            return True, "IP检查通过"
        except Exception as e:
            logging.error(f"IP限制检查失败: {e}")
            return False, f"IP检查出错: {str(e)}"

    def 增加IP设备数(self, ip):
        """增加IP关联的设备数量"""
        try:
            # 查询IP记录
            ip_record = self.数据库.执行单条查询(
                "SELECT 设备数量 FROM 试用账号IP WHERE 试用IP=%s",
                (ip,)
            )

            if ip_record:
                # 更新设备数量
                self.数据库.执行更新(
                    "UPDATE 试用账号IP SET 设备数量=%s, 最近登录时间=%s WHERE 试用IP=%s",
                    (ip_record['设备数量'] + 1, datetime.datetime.now(), ip)
                )
            else:
                # 创建IP记录
                self.数据库.执行更新(
                    "INSERT INTO 试用账号IP(试用IP, 设备数量, IP是否被禁用, 首次登录时间, 最近登录时间) VALUES(%s, 1, 0, %s, %s)",
                    (ip, datetime.datetime.now(), datetime.datetime.now())
                )

            return True
        except Exception as e:
            logging.error(f"增加IP设备数量失败: {e}")
            return False

    def 验证试用账号(self, 机器码, IP):
        """验证试用账号"""
        try:
            # 先检查IP限制
            ip_check, ip_message = self.检查IP限制(IP)
            if not ip_check:
                logging.info(f"IP限制: {IP} - {ip_message}")
                return False, ip_message, None, None, None, 0

            # 查询机器码记录
            existing_record = self.数据库.执行单条查询(
                "SELECT 禁用机器码, 获取次数 FROM 试用账号 WHERE 机器码=%s",
                (机器码,)
            )

            免费已试用次数 = 0
            # 检查机器码状态和获取次数
            if existing_record:
                # 检查机器码是否被禁用
                if existing_record['禁用机器码'] != 0:
                    logging.info(f"机器码已被禁用: {机器码}")
                    return False, "您的设备已被禁用", None, None, None, 免费已试用次数

                # 检查获取次数是否超过限制
                免费已试用次数 = existing_record['获取次数']
                if 免费已试用次数 >= Config.试用账号配置['最大获取次数']:
                    logging.info(f"机器码 {机器码} 已超过最大获取次数: {Config.试用账号配置['最大获取次数']}")
                    return False, "您的免费额度已用完,如需立即使用请升级会员", None, None, None, 免费已试用次数

                # 更新现有记录，包括IP
                self.数据库.执行更新(
                    "UPDATE 试用账号 SET 最近登录=%s, 当前IP=%s WHERE 机器码=%s",
                    (datetime.datetime.now(), IP, 机器码)
                )
            else:
                # 新机器码注册
                # 增加IP设备数量
                self.增加IP设备数(IP)

                # 创建新记录
                self.数据库.执行更新(
                    "INSERT INTO 试用账号(机器码, 创建时间, 最近登录, 获取次数, 禁用机器码, 当前IP) VALUES(%s, %s, %s, 0, 0, %s)",
                    (机器码, datetime.datetime.now(), datetime.datetime.now(), IP)
                )
            
            # 获取账号
            获取成功, 邮箱, 访问令牌, 刷新令牌, 错误类型 = self.获取账号(机器码, False)
        
            if 获取成功:
                # 直接增加试用次数
                try:
                    # 更新获取次数
                    self.数据库.执行更新(
                        "UPDATE 试用账号 SET 获取次数=获取次数+1 WHERE 机器码=%s",
                        (机器码,)
                    )
                    免费已试用次数 += 1
                except Exception as e:
                    logging.error(f"增加试用次数失败: {e}")
                
                return True, f"验证成功，已获取邮箱: {邮箱}", 邮箱, 访问令牌, 刷新令牌, 免费已试用次数
            else:
                logging.warning(f"机器码 {机器码} 获取账号失败: {错误类型}")
                return False, 错误类型, None, None, None, 免费已试用次数
                
        except Exception as e:
            logging.error(f"验证试用账号失败: {e}", exc_info=True)
            return False, f"服务器错误: {str(e)}", None, None, None, 0

    # === 付费账号相关功能 ===
    
    def 清空机器码(self, 卡密):
        """标记卡密对应的机器码为已下线状态"""
        try:
            当前时间 = datetime.datetime.now()
            
            # 检查卡密是否存在
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )
            
            if not 卡密信息:
                return False, "卡密不存在"
                
            # 检查卡密是否绑定了机器码
            if not 卡密信息.get('机器码'):
                return False, "卡密未绑定机器码，无需标记下线"
            
            # 已经是下线状态
            if 卡密信息.get('机器码是否在线') == 0:
                return True, "机器码已是下线状态"
                
            # 标记机器码为下线，并记录下线时间
            self.数据库.执行更新(
                "UPDATE 卡密系统 SET 机器码是否在线=0, 机器码下线时间=%s WHERE 卡密=%s",
                (当前时间, 卡密)
            )
            
            return True, "机器码已标记为下线状态，可以在其他设备上登录"
            
        except Exception as e:
            logging.error(f"标记机器码下线失败: {e}")
            return False, f"标记机器码下线失败: {str(e)}"

    def 检查调用限制(self, 卡密信息):
        """检查卡密调用限制"""
        try:
            当前时间 = datetime.datetime.now()

            # 获取调用次数相关信息
            上次重置时间 = 卡密信息.get('上次重置时间')
            调用次数 = 卡密信息.get('调用次数', 0) or 0

            # 检查二十分钟调用次数
            二十分钟开始时间 = 卡密信息.get('二十分钟开始时间')
            二十分钟调用次数 = 卡密信息.get('二十分钟调用次数', 0) or 0

            # 处理未来时间
            if 上次重置时间 and 上次重置时间 > 当前时间:
                上次重置时间 = 当前时间
                调用次数 = 0
                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 上次重置时间=%s, 调用次数=%s WHERE 卡密=%s",
                    (当前时间, 调用次数, 卡密信息.get('卡密'))
                )

            if 二十分钟开始时间 and 二十分钟开始时间 > 当前时间:
                二十分钟开始时间 = 当前时间
                二十分钟调用次数 = 0
                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 二十分钟开始时间=%s, 二十分钟调用次数=%s WHERE 卡密=%s",
                    (当前时间, 二十分钟调用次数, 卡密信息.get('卡密'))
                )

            二十分钟秒数 = 20 * 60  # 20分钟 = 1200秒
            十分钟秒数 = 10 * 60    # 10分钟 = 600秒
            
            # 如果超过20分钟，重置二十分钟计数器
            if not 二十分钟开始时间 or (当前时间 - 二十分钟开始时间).total_seconds() >= 二十分钟秒数:
                二十分钟调用次数 = 0
                二十分钟开始时间 = 当前时间

                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 二十分钟开始时间=%s, 二十分钟调用次数=%s WHERE 卡密=%s",
                    (二十分钟开始时间, 二十分钟调用次数, 卡密信息.get('卡密'))
                )
            # 检查二十分钟内调用次数是否超过限制
            elif 二十分钟调用次数 >= Config.付费账号配置['二十分钟最大调用次数']:
                # 禁用卡密
                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 禁用卡密=1 WHERE 卡密=%s",
                    (卡密信息.get('卡密'),)
                )
                return False, None

            # 计算下次重置时间
            下次重置时间 = None
            if 上次重置时间:
                下次重置时间 = 上次重置时间 + datetime.timedelta(minutes=10)

            # 检查是否需要重置计数器（10分钟）
            if not 上次重置时间 or (当前时间 - 上次重置时间).total_seconds() >= 十分钟秒数:
                调用次数 = 0
                上次重置时间 = 当前时间
                下次重置时间 = 当前时间 + datetime.timedelta(minutes=10)

                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 上次重置时间=%s, 调用次数=%s WHERE 卡密=%s",
                    (上次重置时间, 调用次数, 卡密信息.get('卡密'))
                )

            # 检查是否超过限制
            if 调用次数 >= Config.付费账号配置['十分钟最大调用次数']:
                return False, 下次重置时间

            return True, 下次重置时间

        except Exception as e:
            logging.error(f"检查调用限制失败: {e}")
            return True, None  # 出错时允许调用，避免阻塞正常功能

    def 检查卡密有效性(self, 卡密, 机器码):
        """检查卡密是否有效，并在首次使用时自动绑定机器码"""
        try:
            当前时间 = datetime.datetime.now()

            # 查询卡密信息
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )

            if not 卡密信息:
                logging.info(f"卡密不存在: {卡密}")
                return False, "卡密不存在", None

            # 检查卡密是否被禁用
            禁用状态 = 卡密信息.get('禁用卡密')
            if 禁用状态 is not None and int(禁用状态) > 0:
                logging.info(f"卡密已被禁用: {卡密}")
                return False, "您的卡密已被禁用，由于被检测频繁大量刷号而未正常使用行为。", 卡密信息

            # 检查卡密是否过期
            到期时间 = 卡密信息.get('到期时间')
            if 到期时间 and 当前时间 > 到期时间:
                logging.info(f"卡密已过期: {卡密}, 过期时间: {到期时间}")
                return False, "卡密已过期", 卡密信息

            # 处理机器码绑定
            绑定成功, 绑定消息 = self.处理卡密机器码绑定(卡密, 机器码, 卡密信息)
            if not 绑定成功:
                if "冷却中" in 绑定消息:
                    剩余分钟 = 绑定消息.split(":")[-1]
                    return False, f"验证失败: 更换设备过于频繁，关闭原设备窗口后等待{剩余分钟}分钟后再试", 卡密信息
                elif 绑定消息 == "设备在线":
                    return False, "验证失败: 此卡密已在其他设备登录, 请关闭原设备窗口再登录", 卡密信息
                else:
                    return False, f"验证失败: {绑定消息}", 卡密信息
                
            # 重新获取更新后的卡密信息
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )
            
            # 检查调用限制
            调用限制结果, 下次重置时间 = self.检查调用限制(卡密信息)
            if 调用限制结果:
                # 获取账号
                获取成功, 邮箱, 访问令牌, 刷新令牌, 错误类型 = self.获取账号(卡密, True)
            
                # 根据获取结果返回不同响应
                if 获取成功:
                    # 更新卡密使用记录
                    self.更新卡密使用记录(卡密, 机器码)
                    
                    # 返回账号信息
                    账号信息 = {
                        "邮箱": 邮箱,
                        "访问令牌": 访问令牌,
                        "刷新令牌": 刷新令牌
                    }
                    logging.info(f"卡密 {卡密} 成功获取账号: {邮箱}")
                    return True, 账号信息, 卡密信息
                else:
                    logging.warning(f"卡密 {卡密} 获取账号失败: {错误类型}")
                    return False, 错误类型, 卡密信息
            else:
                # 设置友好的错误消息，包含下次重置时间
                错误消息 = f"获取频率过快,为避免被恶意使用,影响他人体验"
                if 下次重置时间:
                    错误消息 += f"\n下次重置时间：{下次重置时间.strftime('%H:%M:%S')}"
                logging.info(f"卡密 {卡密} 调用频率限制: {错误消息}")
                return False, 错误消息, 卡密信息

        except Exception as e:
            logging.error(f"检查卡密有效性失败: {e}", exc_info=True)
            return False, f"验证卡密时出错: {str(e)}", None
            
    def 处理卡密机器码绑定(self, 卡密, 机器码, 卡密信息):
        """处理卡密的机器码绑定
        返回：(成功/失败, 状态/错误消息)
        """
        当前时间 = datetime.datetime.now()

        # 减少调试日志，只保留关键信息
        if 卡密信息.get('机器码'):
            # 当前已有机器码
            现有机器码 = 卡密信息.get('机器码', '')
            机器码是否在线 = 卡密信息.get('机器码是否在线', 1)  # 默认在线
            机器码下线时间 = 卡密信息.get('机器码下线时间')

            # 检查是否是相同机器码
            if 现有机器码 == 机器码:
                # 相同机器码，确保在线状态
                if 机器码是否在线 == 0:
                    self.数据库.执行更新(
                        "UPDATE 卡密系统 SET 机器码是否在线=1, 机器码下线时间=NULL WHERE 卡密=%s",
                        (卡密,)
                    )
                return True, "机器码匹配"
            else:
                # 不同机器码
                # 检查是否启用机器码检测
                启用机器码检测 = Config.付费账号配置.get('启用机器码检测', False)

                if 启用机器码检测:
                    # 启用机器码检测时，按原逻辑处理
                    # 如果当前机器码在线，则拒绝新机器码登录
                    if 机器码是否在线 == 1:
                        logging.info(f"卡密 {卡密} 已在其他设备登录: {现有机器码}")
                        return False, "设备在线"

                    # 检查冷却时间
                    if 机器码下线时间:
                        时间差 = (当前时间 - 机器码下线时间).total_seconds() / 60  # 转换为分钟
                        冷却时间 = Config.付费账号配置['换绑冷却时间']  # 从配置获取换绑冷却时间（分钟）
                        if 时间差 < 冷却时间:
                            剩余分钟 = int(冷却时间 - 时间差)
                            logging.info(f"卡密 {卡密} 换绑冷却中，还需等待 {剩余分钟} 分钟")
                            return False, f"冷却中:{剩余分钟}"
                else:
                    # 未启用机器码检测时，允许不同机器码直接登录，无需检查在线状态和冷却时间
                    logging.info(f"卡密 {卡密} 允许不同机器码登录: {机器码} (机器码检测已禁用)")
                    # 直接更新为新机器码，不检查冷却时间
        
        # 执行更新
        try:
            更新结果 = self.数据库.执行更新(
                """
                UPDATE 卡密系统 SET
                    机器码=%s,
                    机器码是否在线=1,
                    机器码下线时间=NULL,
                    最近登录=%s,
                    本月登录次数=CASE
                        WHEN MONTH(最近登录)=MONTH(%s) THEN 本月登录次数+1
                        ELSE 1
                    END
                WHERE 卡密=%s
                """,
                (机器码, 当前时间, 当前时间, 卡密)
            )
                
            return True, "绑定成功"
        except Exception as e:
            logging.error(f"更新机器码失败: {e}", exc_info=True)
            return False, f"绑定失败: {str(e)}"

    def 卡密激活(self, 机器码, 程序名, 类型, 备注="官方版"):
        """生成并激活卡密，绑定机器码"""
        try:
            当前时间 = datetime.datetime.now()
            
            # 生成随机卡密(10位字母数字混合)
            import random
            import string
            字符集 = string.ascii_letters + string.digits
            卡密 = ''.join(random.choice(字符集) for _ in range(10))
            
            # 计算到期时间
            if 类型 == "时卡":
                到期时间 = 当前时间 + datetime.timedelta(hours=1)
            elif 类型 == "天卡":
                到期时间 = 当前时间 + datetime.timedelta(days=1)
            elif 类型 == "周卡":
                到期时间 = 当前时间 + datetime.timedelta(days=7)
            elif 类型 == "月卡":
                到期时间 = 当前时间 + datetime.timedelta(days=30)
            elif 类型 == "年卡":
                到期时间 = 当前时间 + datetime.timedelta(days=365)
            else:
                return False, "验证失败: 无效的卡密类型", None
            
            # 创建新卡密记录
            self.数据库.执行更新(
                """
                INSERT INTO 卡密系统 (
                    卡密, 机器码, 程序名, 首次登录时间, 最近登录,
                    到期时间, 本月登录次数, 最大数量, 类型, 备注
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (卡密, 机器码, 程序名, 当前时间, 当前时间, 到期时间, 1, 1, 类型, 备注)
            )
            
            # 格式化到期时间
            到期时间显示 = 到期时间.strftime("%Y-%m-%d %H:%M:%S")
            
            return True, f"卡密生成成功,到期时间={到期时间显示}", 卡密
            
        except Exception as e:
            logging.error(f"卡密生成失败: {e}")
            return False, f"生成失败: {str(e)}", None

    def 更新卡密使用记录(self, 卡密, 机器码):
        """更新卡密使用记录"""
        try:
            当前时间 = datetime.datetime.now()

            # 获取当前的调用信息
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )

            if not 卡密信息:
                logging.error(f"未找到卡密信息: {卡密}")
                return

            # 获取当前值
            调用次数 = 卡密信息.get('调用次数', 0) or 0
            总调用次数 = 卡密信息.get('总调用次数', 0) or 0
            二十分钟调用次数 = 卡密信息.get('二十分钟调用次数', 0) or 0
            本月登录次数 = 卡密信息.get('本月登录次数', 0) or 0

            # 增加计数
            调用次数 += 1
            总调用次数 += 1
            二十分钟调用次数 += 1
            本月登录次数 += 1

            # 如果时间字段为未来时间，则重置为当前时间
            上次重置时间 = 卡密信息.get('上次重置时间')
            if not 上次重置时间 or 上次重置时间 > 当前时间:
                上次重置时间 = 当前时间

            二十分钟开始时间 = 卡密信息.get('二十分钟开始时间')
            if not 二十分钟开始时间 or 二十分钟开始时间 > 当前时间:
                二十分钟开始时间 = 当前时间

            # 将硬编码的时间值替换为计算值
            十分钟秒数 = 10 * 60  # 10分钟 = 600秒
            
            # 检查是否需要重置计数器（10分钟）
            if 上次重置时间 and (当前时间 - 上次重置时间).total_seconds() >= 十分钟秒数:
                调用次数 = 1  # 重置为1而不是0，因为当前这次调用算一次
                上次重置时间 = 当前时间  # 从当前时间重新开始计时

            # 更新数据库
            self.数据库.执行更新(
                """
                UPDATE 卡密系统
                SET 调用次数=%s, 总调用次数=%s, 上次重置时间=%s,
                    二十分钟调用次数=%s, 二十分钟开始时间=%s,
                    最近登录=%s, 机器码=%s, 本月登录次数=%s
                WHERE 卡密=%s
                """,
                (调用次数, 总调用次数, 当前时间, 二十分钟调用次数, 二十分钟开始时间,
                 当前时间, 机器码, 本月登录次数, 卡密)
            )

        except Exception as e:
            logging.error(f"更新卡密使用记录失败: {e}")

    def 获取卡密信息(self, 卡密=None, 机器码=None):
        """获取卡密信息"""
        try:
            if 卡密:
                return self.数据库.执行单条查询(
                    "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                    (卡密,)
                )
            elif 机器码:
                return self.数据库.执行单条查询(
                    "SELECT * FROM 卡密系统 WHERE 机器码=%s AND 是否使用=1",
                    (机器码,)
                )
            return None
        except Exception as e:
            logging.error(f"获取卡密信息失败: {e}")
            return None

    def 激活现有卡密(self, 卡密, 机器码, 程序名, 备注="官方版"):
        """激活已存在的卡密，绑定机器码和程序名"""
        try:
            当前时间 = datetime.datetime.now()
            
            # 获取卡密信息
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )
            
            if not 卡密信息:
                return False, "卡密不存在", None
                
            # 检查卡密是否已激活
            if 卡密信息.get('首次登录时间') is not None:
                return False, "卡密已被激活", 卡密信息.get('到期时间')
                
            # 优先使用数据库中已有的到期时间
            到期时间 = 卡密信息.get('到期时间')
            if 到期时间:
                卡密类型 = 卡密信息.get('类型')
                if not 卡密类型:
                    return False, "卡密类型不存在，无法激活", None
            else:
                # 获取卡密类型
                卡密类型 = 卡密信息.get('类型')
                
                # 检查类型是否存在
                if not 卡密类型:
                    return False, "卡密类型不存在，无法激活", None
                    
                # 检查类型是否有效
                有效类型列表 = ["时卡", "天卡", "周卡", "月卡", "年卡"]
                if 卡密类型 not in 有效类型列表:
                    return False, f"无效的卡密类型：{卡密类型}", None
                
                # 根据类型计算到期时间
                if 卡密类型 == "时卡":
                    到期时间 = 当前时间 + datetime.timedelta(hours=1)
                elif 卡密类型 == "天卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=1)
                elif 卡密类型 == "周卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=7)
                elif 卡密类型 == "月卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=30)
                elif 卡密类型 == "年卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=365)
            
            # 更新卡密信息
            self.数据库.执行更新(
                """
                UPDATE 卡密系统 SET
                    机器码=%s,
                    程序名=%s,
                    首次登录时间=%s,
                    最近登录=%s,
                    到期时间=%s,
                    本月登录次数=1,
                    最大数量=1,
                    类型=%s,
                    备注=%s
                WHERE 卡密=%s
                """,
                (机器码, 程序名, 当前时间, 当前时间, 到期时间, 卡密类型, 备注, 卡密)
            )
            
            # 格式化到期时间
            到期时间显示 = 到期时间.strftime("%Y-%m-%d %H:%M:%S")
            
            return True, f"卡密激活成功,到期时间={到期时间显示}", 到期时间
            
        except Exception as e:
            logging.error(f"激活现有卡密失败: {e}")
            return False, f"激活失败: {str(e)}", None

    def 启动验证(self, 程序名, 备注, 机器码=None, 卡密=None):
        """启动检查方法，智能判断卡密是否已激活，未激活时自动执行激活"""
        try:
            # 如果有卡密，检查卡密逻辑
            if 卡密:
                
                # 检查卡密是否存在
                卡密信息 = self.数据库.执行单条查询(
                    "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                    (卡密,)
                )

                if not 卡密信息:
                    return False, "卡密不存在", None, None
                    
                # 判断是否为未激活的新卡密（通过首次登录时间是否为空判断）
                是新卡 = 卡密信息.get('首次登录时间') is None
                
                # 如果是新卡密，需要执行激活流程
                if 是新卡:
                    # 验证激活必要参数
                    if not 机器码:
                        return False, "激活卡密需要提供机器码", None, None
                    
                    # 执行激活
                    result, message, 到期时间 = self.激活现有卡密(
                        卡密, 机器码, 程序名, 备注
                    )
                    
                    if result:
                        enable_purchase = 1  # 默认为1
                        
                        # 检查是否需要查询代理秘钥信息
                        if 备注 != "官方版":
                            代理秘钥信息 = self.数据库.执行单条查询(
                                "SELECT 启用购买功能 FROM 代理秘钥 WHERE 代理秘钥=%s",
                                (备注,)
                            )
                            if 代理秘钥信息 and '启用购买功能' in 代理秘钥信息:
                                enable_purchase = 代理秘钥信息['启用购买功能']
                        
                        return True, message, 到期时间, enable_purchase
                    else:
                        return False, message, None, None
                
                # 已激活卡密的处理逻辑
                
                # 如果程序名不为空且不匹配
                if 卡密信息.get('程序名') and 卡密信息.get('程序名') != 程序名:
                    return False, "此卡密不是本程序的卡密", None, None
                    
                # 如果程序名为空，更新程序名
                if 卡密信息.get('程序名') is None or 卡密信息.get('程序名') == '':
                    self.数据库.执行更新(
                        "UPDATE 卡密系统 SET 程序名=%s WHERE 卡密=%s",
                        (程序名, 卡密)
                    )
                    
                    # 重新获取卡密信息
                    卡密信息 = self.数据库.执行单条查询(
                        "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                        (卡密,)
                    )
                    
                # 检查卡密是否被禁用
                禁用状态 = 卡密信息.get('禁用卡密')
                if 禁用状态 is not None and int(禁用状态) > 0:
                    return False, "您的卡密已被禁用，由于被检测频繁大量刷号而未正常使用行为。", None, None
                
                # 检查卡密是否过期
                当前时间 = datetime.datetime.now()
                到期时间 = 卡密信息.get('到期时间')
                if 到期时间 and 当前时间 > 到期时间:
                    return False, "卡密已过期", 到期时间, None
                
                # 构建响应数据
                enable_purchase = 1  # 默认为1
                
                # 使用传入的备注参数查询代理秘钥表
                if 备注 and 备注 != "官方版":
                    代理秘钥信息 = self.数据库.执行单条查询(
                        "SELECT 启用购买功能 FROM 代理秘钥 WHERE 代理秘钥=%s",
                        (备注,)
                    )
                    if 代理秘钥信息 and '启用购买功能' in 代理秘钥信息:
                        enable_purchase = 代理秘钥信息['启用购买功能']
                
                # 检查通过，返回成功
                return True, "卡密验证通过", 到期时间, enable_purchase
            
            # 无卡密情况，只检查备注的启用购买功能
            enable_purchase = 1  # 默认为1
            
            # 使用传入的备注参数查询代理秘钥表
            if 备注 and 备注 != "官方版":
                代理秘钥信息 = self.数据库.执行单条查询(
                    "SELECT 启用购买功能 FROM 代理秘钥 WHERE 代理秘钥=%s",
                    (备注,)
                )
                if 代理秘钥信息 and '启用购买功能' in 代理秘钥信息:
                    enable_purchase = 代理秘钥信息['启用购买功能']
            
            # 无卡密情况下返回成功
            return True, "启动检查通过", None, enable_purchase
                
        except Exception as e:
            logging.error(f"启动检查错误: {str(e)}", exc_info=True)
            return False, f"服务器错误: {str(e)}", None, None

    def 维持10个可用账号(self):
        """检测邮箱系统中的账号可用性并确保至少有10个可用账号
        采用缓存策略：只检测未验证过的账号，直到可用账号达到10个为止
        """
        当前时间 = datetime.datetime.now()
        
        # 先检查现有缓存的可用性
        if 账号管理器.可用账号缓存:
            # 检查缓存中的账号是否仍然可用（随机抽查一个账号）
            import random
            抽查账号 = random.choice(账号管理器.可用账号缓存)
            try:
                is_authorized, _ = self.检查账号授权(抽查账号['访问令牌'])
                if not is_authorized:
                    # 抽查账号不可用，可能其他账号也失效，清空缓存重新检测
                    logging.warning(f"缓存账号 {抽查账号['邮箱']} 已失效，清空缓存并重新检测")
                    账号管理器.可用账号缓存 = []
            except Exception:
                # 检测出错，为安全起见清空缓存
                账号管理器.可用账号缓存 = []
        
        # 过滤出仍然可用的账号
        有效缓存账号 = []
        for 账号 in 账号管理器.可用账号缓存:
            try:
                is_authorized, _ = self.检查账号授权(账号['访问令牌'])
                if is_authorized:
                    有效缓存账号.append(账号)
                else:
                    logging.warning(f"缓存账号 {账号['邮箱']} 已失效，从缓存中移除")
                    # 从数据库中删除
                    self.删除异常账号(账号['邮箱'], 账号['访问令牌'], 账号['刷新令牌'], "缓存检测失效", "自动检测")
            except Exception as e:
                logging.error(f"检测缓存账号 {账号['邮箱']} 时出错: {e}")
        
        # 更新可用账号缓存
        账号管理器.可用账号缓存 = 有效缓存账号
        
        # 如果可用账号已经足够，直接返回
        if len(账号管理器.可用账号缓存) >= 10:
            logging.info(f"当前缓存可用账号数量已足够: {len(账号管理器.可用账号缓存)}个")
            return len(账号管理器.可用账号缓存)
            
        # 缓存数量不足，需要补充可用账号
        # 计算需要补充的账号数量
        需要补充数量 = 10 - len(账号管理器.可用账号缓存)
        logging.info(f"当前缓存可用账号数量: {len(账号管理器.可用账号缓存)}个，需要补充{需要补充数量}个")
        
        # 已有的可用邮箱列表，用于避免重复检测
        已有邮箱 = {账号['邮箱'] for 账号 in 账号管理器.可用账号缓存}
        
        # 获取所有创建时间在2025-05-16及之后的邮箱账号
        邮箱列表 = self.数据库.执行查询(
            """
            SELECT 邮箱, 访问令牌, 刷新令牌
            FROM 邮箱系统
            WHERE 邮箱 IS NOT NULL
            AND 访问令牌 IS NOT NULL
            AND 刷新令牌 IS NOT NULL
            AND 创建时间 >= '2025-05-16'
            ORDER BY 创建时间 ASC
            """
        )
        
        # 检测新账号，直到补充足够的可用账号
        已补充数量 = 0
        
        for 邮箱信息 in 邮箱列表:
            邮箱 = 邮箱信息.get('邮箱')
            访问令牌 = 邮箱信息.get('访问令牌')
            刷新令牌 = 邮箱信息.get('刷新令牌')
            
            # 跳过已缓存的邮箱
            if 邮箱 in 已有邮箱:
                continue
                
            if not 访问令牌:
                continue
                
            try:
                # 检查账号是否授权
                is_authorized, status = self.检查账号授权(访问令牌)
                
                if is_authorized:
                    # 添加到可用账号缓存
                    账号管理器.可用账号缓存.append({
                        '邮箱': 邮箱,
                        '访问令牌': 访问令牌,
                        '刷新令牌': 刷新令牌,
                        '检测时间': 当前时间
                    })
                    已补充数量 += 1
                    logging.info(f"补充可用账号: {邮箱}")
                    
                    # 如果已经补充足够数量，结束检测
                    if 已补充数量 >= 需要补充数量:
                        break
                else:
                    logging.warning(f"账号 {邮箱} 已失效: {status}")
                    self.删除异常账号(邮箱, 访问令牌, 刷新令牌, status, "自动检测")
            except Exception as e:
                logging.error(f"检测账号 {邮箱} 时出错: {e}")
        
        可用账号数量 = len(账号管理器.可用账号缓存)
        logging.info(f"账号检测完成，当前可用账号数量: {可用账号数量}")
        
        # 记录上次检测时间
        账号管理器.上次检测时间 = 当前时间
        
        # 确保至少有10个可用账号
        if 可用账号数量 < 10:
            logging.warning(f"可用账号数量不足10个，当前只有{可用账号数量}个")
            # 可以添加补充账号的逻辑或发送通知
        
        return 可用账号数量

# 添加系统配置管理器类
class 系统配置管理器:
    """系统配置管理器，负责获取数据库中的动态配置"""
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()

    def 检查系统配置(self):
        """
        检查全局配置（开启免费号池和付费号池，以及Windows和Mac版本号）
        :return: (开启免费号池, 开启付费号池, Windows版本号, Mac版本号)
        """
        try:
            # 从cursorpro表查询配置
            配置 = self.数据库.执行单条查询(
                "SELECT 开启免费号池, 开启付费号池, 网络版本号_Windows, 网络版本号_Mac FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            if 配置:
                开启免费号池 = 配置.get('开启免费号池', 0)
                开启付费号池 = 配置.get('开启付费号池', 0)
                网络版本号_Windows = 配置.get('网络版本号_Windows', '')
                网络版本号_Mac = 配置.get('网络版本号_Mac', '')
                return 开启免费号池, 开启付费号池, 网络版本号_Windows, 网络版本号_Mac

            return 0, 0, '', ''  # 默认都关闭，版本号为空字符串
        except Exception as e:
            logging.error(f"检查系统配置失败: {e}")
            return 0, 0, '', ''  # 默认都关闭，版本号为空字符串

#账号数据是:邮箱 访问令牌 刷新令牌,用户标识:免费则是机器码,付费则是卡密
class API服务:
    """API服务类，处理HTTP请求"""
    def __init__(self):
        self.app = Flask(__name__)
        self.数据库 = 数据库管理器()
        self.账号管理器 = 账号管理器(self.数据库)
        
        # 定期任务设置
        self.设置定期任务()
        
        # 注册路由
        self.注册路由()
        
        # 添加请求日志中间件
        @self.app.before_request
        def log_request_info():
            # 只记录请求路径，不记录详细数据
            logging.info(f"收到请求: {request.method} {request.url}")
        
        @self.app.after_request
        def log_response_info(response):
            # 只记录响应状态，不记录响应数据
            if response.status_code != 200:
                logging.info(f"响应状态: {response.status}")
            return response
        
        logging.info("API服务初始化完成，连接池已创建")
    
    def 设置定期任务(self):
        """设置定期执行的任务"""
        import threading
        
        def 定期任务():
            """定期执行的任务，包括缓存清理和账号检测"""
            while True:
                try:
                    # 清理过期缓存
                    缓存.清理过期缓存()
                    
                    # 检测账号可用性并确保可用账号数量
                    try:
                        self.账号管理器.维持10个可用账号()
                    except Exception as e:
                        logging.error(f"账号检测任务执行失败: {e}")
                        
                except Exception as e:
                    logging.error(f"定期任务执行失败: {e}")
                    
                time.sleep(5)  # 每5秒执行一次
        
        # 启动定期任务线程
        任务线程 = threading.Thread(target=定期任务, daemon=True)
        任务线程.start()
        logging.info("定期任务已启动，包括缓存清理和账号检测")
    
    def 注册路由(self):
        """注册API路由"""
        # 统一API端点
        @self.app.route('/api/account', methods=['POST'])
        def 账号管理():
            """统一的账号管理API端点，处理各种账号相关操作"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'success': False, 'message': '缺少请求数据'}), 400
                
                # 通过操作类型参数区分不同操作
                操作类型 = data.get('operation', '')
                
                # 根据操作类型调用不同的处理方法
                if 操作类型 == 'verify_trial':
                    return self.验证试用账号(data)
                elif 操作类型 == 'verify_paid':
                    return self.验证付费账号(data)
                elif 操作类型 == 'activate_key':
                    return self.激活卡密(data)
                elif 操作类型 == 'check_startup':
                    return self.启动检查(data)
                elif 操作类型 == 'clear_machine_code':
                    return self.清空机器码(data)
                else:
                    return jsonify({'success': False, 'message': '未知的操作类型'}), 400
            except Exception as e:
                logging.error(f"API请求处理失败: {e}", exc_info=True)
                return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500
    
    
    # API处理方法
    def 验证试用账号(self, data):
        """处理验证试用账号请求"""
        # 减少日志，只记录关键信息
        if not data:
            logging.error("验证试用账号请求：缺少请求数据")
            return jsonify({'success': False, 'message': '缺少请求数据', 'trial_count': 0}), 400

        # 验证必填参数
        必填参数 = ['machine_code', 'ip', 'version', 'platform']
        缺少参数 = [param for param in 必填参数 if param not in data]
        if 缺少参数:
            缺少参数字符串 = ', '.join(缺少参数)
            logging.error(f"验证试用账号请求：缺少参数: {缺少参数字符串}")
            return jsonify({
                'success': False,
                'message': f'缺少参数: {缺少参数字符串}',
                'trial_count': 0
            }), 400

        # 检查系统配置和版本号
        系统配置 = self.账号管理器.检查系统配置()
        开启免费号池 = 系统配置.get('开启免费号池', 0)
        网络版本号_Windows = 系统配置.get('网络版本号_Windows', '')
        网络版本号_Mac = 系统配置.get('网络版本号_Mac', '')
        
        logging.info(f"系统配置: 开启免费号池={开启免费号池}, Windows版本={网络版本号_Windows}, Mac版本={网络版本号_Mac}")
        
        # 检查版本号
        客户端版本 = data['version']
        平台 = data['platform']
        服务器版本 = 网络版本号_Windows if 平台 == 'Windows' else 网络版本号_Mac
        
        logging.info(f"版本检查: 客户端版本={客户端版本}, 平台={平台}, 服务器版本={服务器版本}")
        
        # 如果版本号不匹配，提示更新
        if 服务器版本 and 客户端版本 < 服务器版本:
            logging.info(f"版本过低: 客户端版本={客户端版本}, 服务器版本={服务器版本}")
            return jsonify({
                'success': False,
                'message': f'当前版本过低，请更新到最新版本 {服务器版本}',
                'trial_count': 0,
                'need_update': True,
                'latest_version': 服务器版本
            })
        
        # 检查免费号池状态
        if 开启免费号池 == 0:
            logging.info("免费号池未开启")
            return jsonify({
                'success': False,
                'message': '当前免费号池资源紧张,如需立即使用请升级会员',
                'trial_count': 0
            })

        # 直接传递机器码和IP参数给验证方法
        机器码 = data['machine_code']
        IP = data['ip']
        
        result, message, 邮箱, 访问令牌, 刷新令牌, 免费已试用次数 = self.账号管理器.验证试用账号(机器码, IP)
        
        logging.info(f"验证结果: result={result}, message={message}, 免费已试用次数={免费已试用次数}")

        if result:
            账号信息 = {"邮箱": 邮箱, "访问令牌": 访问令牌, "刷新令牌": 刷新令牌}
            logging.info(f"成功获取账号: {邮箱}")
            return jsonify({
                'success': True,
                'message': message,
                'trial_count': 免费已试用次数,
                'account_info': 账号信息
            })
        else:
            # 只记录失败情况
            logging.info(f"获取失败: {机器码}, 原因: {message}")
            return jsonify({'success': False, 'message': message, 'trial_count': 0})

    def 验证付费账号(self, data):
        """处理验证付费账号请求"""
        if not data:
            return jsonify({'success': False, 'message': '缺少请求数据'}), 400

        # 验证必填参数
        必填参数 = ['key', 'machine_code', 'version', 'platform']
        缺少参数 = [param for param in 必填参数 if param not in data]
        if 缺少参数:
            缺少参数字符串 = ', '.join(缺少参数)
            return jsonify({
                'success': False,
                'message': f'缺少参数: {缺少参数字符串}'
            }), 400

        # 检查系统配置
        系统配置 = self.账号管理器.检查系统配置()
        开启付费号池 = 系统配置.get('开启付费号池', 0)
        网络版本号_Windows = 系统配置.get('网络版本号_Windows', '')
        网络版本号_Mac = 系统配置.get('网络版本号_Mac', '')
        
        # 检查版本号
        客户端版本 = data['version']
        平台 = data['platform']
        服务器版本 = 网络版本号_Windows if 平台 == 'Windows' else 网络版本号_Mac
        
        # 如果版本不匹配，提示更新
        if 服务器版本 and 客户端版本 < 服务器版本:
            return jsonify({
                'success': False,
                'message': f'当前版本过低，请更新到最新版本 {服务器版本}',
                'need_update': True,
                'latest_version': 服务器版本
            })
        
        if 开启付费号池 == 0:
            return jsonify({
                'success': False,
                'message': '当前付费号池资源紧张,请稍后再试'
            })

        # 验证卡密
        result, message_or_account, 卡密信息 = self.账号管理器.检查卡密有效性(
            data['key'], data['machine_code']
        )

        if result:
            # 转换卡密信息为可JSON序列化的格式
            可序列化信息 = {}
            if 卡密信息:
                for key, value in 卡密信息.items():
                    if isinstance(value, datetime.datetime):
                        可序列化信息[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        # 对于数据库读取的字符串类型（包括月卡和年卡），直接添加到字典
                        可序列化信息[key] = value

            # 处理返回的账号信息
            账号信息 = None
            消息 = "卡密验证成功"
            
            if isinstance(message_or_account, dict) and "邮箱" in message_or_account:
                账号信息 = message_or_account
            else:
                消息 = message_or_account

            return jsonify({
                'success': True,
                'message': 消息,
                'key_info': 可序列化信息,
                'account_info': 账号信息
            })
        else:
            return jsonify({'success': False, 'message': message_or_account})

    def 激活卡密(self, data):
        """处理卡密激活请求"""
        if not data:
            return jsonify({'success': False, 'message': '缺少请求数据'}), 400

        # 验证必填参数
        if 'machine_code' not in data:
            return jsonify({'success': False, 'message': '缺少机器码参数'}), 400
        if 'app_name' not in data:
            return jsonify({'success': False, 'message': '缺少程序名称参数'}), 400
        if 'key_type' not in data:
            return jsonify({'success': False, 'message': '缺少卡密类型参数'}), 400

        # 获取备注参数（可选）
        备注 = data.get('remark', '官方版')

        # 检查系统配置
        系统配置 = self.账号管理器.检查系统配置()
        开启付费号池 = 系统配置.get('开启付费号池', 0)
        if 开启付费号池 == 0:
            return jsonify({
                'success': False,
                'message': '当前付费号池资源紧张,请稍后再试'
            })

        # 激活卡密
        result, message, 卡密 = self.账号管理器.卡密激活(
            data['machine_code'], data['app_name'], data['key_type'], 备注
        )

        if result:
            return jsonify({'success': True, 'message': message, 'key': 卡密})
        else:
            return jsonify({'success': False, 'message': message})

    def 启动检查(self, data):
        """处理启动检查请求"""
        if not data:
            return jsonify({'success': False, 'message': '缺少请求数据'}), 400

        # 验证必填参数
        if 'app_name' not in data:
            return jsonify({'success': False, 'message': '缺少程序名称参数'})
        if 'remark' not in data:
            return jsonify({'success': False, 'message': '缺少备注参数'})

        程序名 = data['app_name']
        备注 = data['remark']
        机器码 = data.get('machine_code')
        卡密 = data.get('key')
        
        # 执行启动检查
        result, message, 到期时间, enable_purchase = self.账号管理器.启动验证(
            程序名, 备注, 机器码, 卡密
        )
        
        if result:
            # 获取系统配置，包含月卡和年卡
            系统配置 = self.账号管理器.检查系统配置()
            月卡 = 系统配置.get('月卡', None)
            年卡 = 系统配置.get('年卡', None)
            
            response_data = {
                'success': True,
                'message': message,
                'enable_purchase': enable_purchase
            }
            
            # 如果有到期时间，添加到响应
            if 到期时间:
                if isinstance(到期时间, datetime.datetime):
                    response_data['expiry_time'] = 到期时间.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    response_data['expiry_time'] = 到期时间
                    
            # 如果月卡和年卡不为 None，则添加到返回数据中
            if 月卡 is not None:
                response_data['月卡'] = 月卡
            if 年卡 is not None:
                response_data['年卡'] = 年卡
            
            return jsonify(response_data)
        else:
            return jsonify({'success': False, 'message': message})

    def 清空机器码(self, data):
        """处理机器码下线请求"""
        if not data:
            return jsonify({'success': False, 'message': '缺少请求数据'}), 400
            
        # 验证必填参数
        if 'key' not in data:
            return jsonify({'success': False, 'message': '缺少卡密参数'}), 400
            
        # 执行机器码下线
        result, message = self.账号管理器.清空机器码(data['key'])
        return jsonify({'success': result, 'message': message})

    def 运行(self, host=None, port=None, debug=None):
        """启动API服务"""
        host = host or Config.API服务配置['监听地址']
        port = port or Config.API服务配置['端口']
        debug = debug if debug is not None else Config.API服务配置['调试模式']
        
        self.app.run(host=host, port=port, debug=debug)

# 启动应用
if __name__ == '__main__':
    服务 = API服务()
    服务.运行()