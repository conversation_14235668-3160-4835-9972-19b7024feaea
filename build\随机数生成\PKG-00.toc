('D:\\PythonProject\\服务验证端\\build\\随机数生成\\随机数生成.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'D:\\PythonProject\\服务验证端\\build\\随机数生成\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'D:\\PythonProject\\服务验证端\\build\\随机数生成\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\PythonProject\\服务验证端\\build\\随机数生成\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\PythonProject\\服务验证端\\build\\随机数生成\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\PythonProject\\服务验证端\\build\\随机数生成\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\PythonProject\\服务验证端\\build\\随机数生成\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('随机数生成', 'D:\\PythonProject\\服务验证端\\随机数生成.py', 'PYSOURCE'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\LLVM\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'D:\\PythonProject\\服务验证端\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('base_library.zip',
   'D:\\PythonProject\\服务验证端\\build\\随机数生成\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
