import json
import random
import time
import datetime
import logging
import webbrowser
import string
import mysql.connector

from flask import request, jsonify, Blueprint
import os

# 配置日志 (可以在主程序中统一配置)
# logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class Config:
    """系统配置类"""
    # 支付宝配置 (管理员模块不需要，但其他类可能需要，保留)
    支付宝配置 = {
        'app_id': "", # 管理员模块不使用支付功能，留空或移除
        'private_key': "",
        'alipay_public_key': "",
        'app_name': "卡密验证系统",
        'return_url': "",
        'notify_url': ""
    }
    
    # 卡密类型配置
    卡密类型 = {
        '有效类型': ['时卡', '天卡', '周卡', '月卡', '季卡', '年卡'],
        '类型价格': {
            '时卡': 2.0,
            '天卡': 6.6,
            '周卡': 26.9,
            '月卡': 49.9,
            '季卡': 89.9,
            '年卡': 360.0 # 这里可能需要更新为实际后台配置的价格
        }
    }
    
    # 数据库配置
    数据库配置 = {
        'host': 'localhost', # 需要根据实际环境修改
        'port': 3306,
        'user': 'root', # 需要根据实际环境修改
        'password': 'YU6709', # 需要根据实际环境修改
        'database': 'kami3162' # 需要根据实际环境修改
    }
    
    # 服务配置 (管理员模块不需要)
    服务配置 = {
        'host': '0.0.0.0',
        'port': 5002,
        'debug': False,
        'public_host': '',
        'public_port': 5002
    }
    
    @classmethod
    def get_public_url(cls):
        """获取公网URL，用于支付回调等场景 (管理员模块不使用)"""
        # return f"http://{cls.服务配置['public_host']}:{cls.服务配置['public_port']}"
        return ""

class 数据库管理器:
    """数据库连接和操作管理"""
    def __init__(self, 配置=None):
        # 尝试使用Config中的数据库配置，如果提供了配置则使用提供的
        self.配置 = 配置 or Config.数据库配置

    def 获取连接(self):
        """获取数据库连接"""
        try:
            # 添加错误处理，避免连接失败导致程序崩溃
            return mysql.connector.connect(**self.配置)
        except mysql.connector.Error as err:
            logging.error(f"数据库连接失败: {err}")
            # 可以选择重新抛出异常或返回 None/特殊值
            raise err

    def 执行查询(self, sql, 参数=None):
        """执行查询并返回结果"""
        conn = None
        cursor = None
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchall()
            return result
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn and conn.is_connected():
                conn.close()

    def 执行单条查询(self, sql, 参数=None):
        """执行查询并返回单条结果"""
        conn = None
        cursor = None
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchone()
            return result
        except Exception as e:
            logging.error(f"单条查询执行失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn and conn.is_connected():
                conn.close()

    def 执行更新(self, sql, 参数=None):
        """执行更新操作"""
        conn = None
        cursor = None
        try:
            conn = self.获取连接()
            cursor = conn.cursor()
            cursor.execute(sql, 参数 or ())
            conn.commit()
            affected_rows = cursor.rowcount
            return affected_rows
        except Exception as e:
            # 发生异常时回滚事务
            if conn:
                conn.rollback()
            logging.error(f"更新执行失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn and conn.is_connected():
                conn.close()
            
    def 在事务中执行(self, 回调函数):
        """
        在事务中执行一系列数据库操作
        
        参数:
            回调函数: 接收连接和游标的函数，在其中执行SQL操作
        
        返回:
            回调函数的返回值
        """
        conn = None
        cursor = None
        try:
            conn = self.获取连接()
            conn.autocommit = False  # 关闭自动提交
            cursor = conn.cursor(dictionary=True) # 事务中也使用字典游标方便回调函数处理
            
            # 执行回调函数，传入连接和游标
            result = 回调函数(conn, cursor)
            
            # 提交事务
            conn.commit()
            return result
        except Exception as e:
            # 发生异常，回滚事务
            if conn:
                conn.rollback()
            logging.error(f"事务执行失败: {e}")
            raise
        finally:
            # 确保关闭游标和连接
            if cursor:
                cursor.close()
            if conn and conn.is_connected():
                conn.close()

class 卡密管理器:
    """卡密管理类，提供卡密相关功能"""
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
        self.卡密类型配置 = Config.卡密类型
    
    def _计算到期时间(self, 卡密类型, 当前时间=None):
        """
        根据卡密类型计算到期时间

        参数:
            卡密类型: 卡密类型（时卡、天卡、周卡、月卡、季卡、年卡）
            当前时间: 当前时间，默认为None，将使用当前系统时间

        返回:
            到期时间对象和格式化的到期时间字符串
        """
        当前时间 = 当前时间 or datetime.datetime.now()
        
        到期时间映射 = {
            "时卡": datetime.timedelta(hours=1),
            "天卡": datetime.timedelta(days=1),
            "周卡": datetime.timedelta(days=7),
            "月卡": datetime.timedelta(days=30),
            "季卡": datetime.timedelta(days=90),
            "年卡": datetime.timedelta(days=365)
        }
        
        到期时间 = 当前时间 + 到期时间映射.get(卡密类型, datetime.timedelta(days=1)) # 默认1天
        到期时间字符串 = 到期时间.strftime('%Y-%m-%d %H:%M:%S')
        
        return 到期时间, 到期时间字符串
    
    def _生成随机卡密(self, 长度=10):
        """
        生成随机卡密
        
        参数:
            长度: 卡密长度，默认10位
            
        返回:
            生成的卡密
        """
        字符集 = string.ascii_letters + string.digits
        卡密 = ''.join(random.choice(字符集) for _ in range(长度))
        return 卡密

    def _验证卡密字符串(self, 卡密):
        """
        验证卡密字符串格式是否有效
        
        参数:
            卡密: 要验证的卡密字符串
            
        返回:
            bool: 卡密格式是否有效
        """
        # 检查基本长度要求
        if not 卡密 or len(卡密) < 4:
            return False
            
        # 直接检查是否全部是字母和数字
        return all(c in string.ascii_letters + string.digits for c in 卡密)

    def 查询卡密状态(self, 卡密):
        """
        查询卡密状态
        
        参数:
            卡密: 要查询的卡密
            
        返回:
            (成功状态, 消息, 卡密数据)
        """
        try:
            # 查询卡密信息
            查询SQL = """
                SELECT 卡密, 类型, 机器码, 首次登录时间, 最近登录, 到期时间, 本月登录次数, 最大数量, 备注
                FROM 卡密系统
                WHERE 卡密 = %s
                LIMIT 1
            """
            结果 = self.数据库.执行单条查询(查询SQL, [卡密])
            
            if not 结果:
                return False, "卡密不存在", None
                
            return True, "查询成功", 结果
            
        except Exception as e:
            logging.error(f"查询卡密状态失败: {e}")
            return False, f"查询失败: {str(e)}", None

    def 搜索卡密按到期时间(self, 开始日期=None, 结束日期=None, 关键词=None):
        """
        根据到期时间范围和关键词搜索卡密 (保留原方法，但主要使用下面的简化搜索方法)
        """
        try:
            sql = """
                SELECT 卡密, 类型, 机器码, 首次登录时间, 最近登录, 到期时间, 本月登录次数, 最大数量, 备注
                FROM 卡密系统
                WHERE 1=1
            """
            参数 = []
            
            if 开始日期:
                sql += " AND 到期时间 >= %s"
                参数.append(f"{开始日期} 00:00:00")
                
            if 结束日期:
                sql += " AND 到期时间 <= %s"
                参数.append(f"{结束日期} 23:59:59")
                
            if 关键词:
                sql += " AND (卡密 LIKE %s OR 备注 LIKE %s OR 机器码 LIKE %s)"
                参数.extend([f"%{关键词}%", f"%{关键词}%", f"%{关键词}%"])
                
            sql += " ORDER BY 到期时间 DESC"
            
            结果 = self.数据库.执行查询(sql, 参数)
            
            return True, "查询成功", 结果
            
        except Exception as e:
            logging.error(f"搜索卡密失败: {e}")
            return False, f"搜索失败: {str(e)}", None
            
    def 搜索卡密按卡密(self, 卡密):
        """
        根据卡密查询卡密信息，仅返回卡密、类型和到期时间
        
        参数:
            卡密: 要查询的卡密
            
        返回:
            (成功状态, 消息, 卡密数据列表 - 最多包含一个字典)
        """
        try:
            # 查询卡密信息，只选择需要的列
            查询SQL = """
                SELECT 卡密, 类型, 到期时间
                FROM 卡密系统
                WHERE 卡密 = %s
                LIMIT 1
            """
            结果 = self.数据库.执行单条查询(查询SQL, [卡密])
            
            if not 结果:
                return True, "未找到该卡密", []
                
            # 返回包含一个字典的列表，与批量查询结果格式一致
            return True, "查询成功", [结果]
            
        except Exception as e:
            logging.error(f"按卡密搜索失败: {e}")
            return False, f"按卡密搜索失败: {str(e)}", []
            
    def 添加卡密时长(self, 卡密, 卡密类型, 数量=1):
        """
        为指定卡密添加时长
        
        参数:
            卡密: 要添加时长的卡密
            卡密类型: 添加的时长类型（时卡、天卡等）
            数量: 添加该类型时长的数量，默认为1
            
        返回:
            (成功状态, 消息, 更新后的卡密信息)
        """
        try:
            # 验证卡密类型是否有效
            有效类型 = self.卡密类型配置['有效类型']
            if 卡密类型 not in 有效类型:
                return False, f"无效的卡密类型，有效类型: {', '.join(有效类型)}", None
                
            # 使用事务确保操作原子性
            def update_card_duration(conn, cursor):
                # 查询卡密信息并锁定行
                cursor.execute("SELECT 到期时间 FROM 卡密系统 WHERE 卡密 = %s FOR UPDATE", (卡密,))
                卡密信息 = cursor.fetchone()
                
                if not 卡密信息:
                    return False, "卡密不存在", None
                
                原到期时间 = 卡密信息.get('到期时间')
                当前时间 = datetime.datetime.now()
                
                # 计算新的到期时间基准：如果原到期时间在未来，则从原到期时间开始加；否则从当前时间开始加
                基准时间 = 原到期时间 if 原到期时间 and 原到期时间 > 当前时间 else 当前时间
                
                新到期时间 = 基准时间
                
                # 根据卡密类型和数量计算总时长
                时长映射 = {
                    "时卡": datetime.timedelta(hours=1),
                    "天卡": datetime.timedelta(days=1),
                    "周卡": datetime.timedelta(days=7),
                    "月卡": datetime.timedelta(days=30),
                    "年卡": datetime.timedelta(days=365)
                }
                
                if 卡密类型 in 时长映射:
                    增加时长 = 时长映射[卡密类型] * 数量
                    新到期时间 += 增加时长
                else:
                     return False, f"无法为类型 '{卡密类型}' 添加时长", None
                
                新到期时间字符串 = 新到期时间.strftime('%Y-%m-%d %H:%M:%S')
                
                # 更新卡密到期时间
                cursor.execute(
                    "UPDATE 卡密系统 SET 到期时间 = %s WHERE 卡密 = %s",
                    (新到期时间字符串, 卡密)
                )
                
                # 重新查询更新后的卡密信息返回
                cursor.execute("SELECT 卡密, 类型, 机器码, 首次登录时间, 最近登录, 到期时间, 本月登录次数, 最大数量, 备注 FROM 卡密系统 WHERE 卡密 = %s", (卡密,))
                更新后卡密信息 = cursor.fetchone()
                
                return True, "时长添加成功", 更新后卡密信息
                
            # 在事务中执行更新，并获取结果
            成功, 消息, 更新后卡密信息 = self.数据库.在事务中执行(update_card_duration)
            return 成功, 消息, 更新后卡密信息 # 直接返回事务执行结果
            
        except Exception as e:
            logging.error(f"添加卡密时长失败: {e}")
            return False, f"添加时长失败: {str(e)}", None

class 支付API处理:
    """支付API相关处理函数 - 仅包含管理员相关部分"""
    def __init__(self, 数据库=None, 卡密管理=None):
        self.数据库 = 数据库 or 数据库管理器()
        self.卡密管理 = 卡密管理 or 卡密管理器(self.数据库)
        # 管理员部分不需要支付宝支付系统实例
        
    def _管理员接口响应(self, 成功状态, 消息, 数据列表):
        """生成标准格式的管理员接口响应"""
        response = jsonify({
            'success': 成功状态,
            'message': 消息,
            'data': 数据列表
        })
        # 允许跨域访问
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, GET, OPTIONS') # 允许多种方法
        return response

    # 管理员批量生成卡密API
    def admin_generate_cards(self):
        """管理员批量生成卡密API，接收POST请求"""
        try:
            # 处理跨域请求
            if request.method == 'OPTIONS':
                response = self._管理员接口响应(True, "跨域预检成功", [])
                return response
                
            # 处理POST请求
            if request.method == 'POST':
                data = request.get_json()
                if not data:
                    return self._管理员接口响应(False, "缺少请求数据", [])
                
                # 获取参数
                卡密类型 = data.get('card_type')
                数量 = data.get('quantity', 1)
                备注 = data.get('remark', '批量生成')
                
                # 验证数量参数
                try:
                    数量 = int(数量)
                    if 数量 < 1 or 数量 > 1000: # 适当放宽数量上限，但仍需控制
                        return self._管理员接口响应(False, "数量必须在1-1000之间", [])
                except:
                    return self._管理员接口响应(False, "无效的数量参数", [])
                
                # 验证卡密类型
                有效类型 = Config.卡密类型['有效类型']
                if 卡密类型 not in 有效类型:
                    return self._管理员接口响应(False, f"无效的卡密类型，有效类型: {', '.join(有效类型)}", [])
                
                # 生成卡密逻辑
                生成的卡密列表 = []
                当前时间 = datetime.datetime.now()
                当前时间字符串 = 当前时间.strftime('%Y-%m-%d %H:%M:%S')
                
                # 管理员卡密不设置到期时间
                # 卡密管理器实例 = 卡密管理器() # 使用self.卡密管理
                
                # 使用事务生成卡密
                def 生成卡密事务(conn, cursor):
                    生成结果 = []
                    成功生成数量 = 0
                    
                    for _ in range(数量):
                        if 成功生成数量 >= 数量: # 确保不会生成超过请求数量
                            break

                        # 生成随机卡密
                        新卡密 = self.卡密管理._生成随机卡密(10) # 使用self.卡密管理的方法
                        
                        # 检查卡密是否已存在
                        cursor.execute("SELECT 卡密 FROM 卡密系统 WHERE 卡密 = %s", (新卡密,))
                        if cursor.fetchone():
                            continue  # 卡密已存在，重新生成
                        
                        # 插入数据库
                        cursor.execute(
                            """
                            INSERT INTO 卡密系统 (
                                卡密, 机器码, 程序名, 首次登录时间, 最近登录,
                                到期时间, 本月登录次数, 最大数量, 类型, 备注
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """,
                            (新卡密, "", "", None, None, None, 0, 1, 卡密类型, 备注)
                        )
                        
                        # 添加到结果列表
                        生成结果.append({
                            "卡密": 新卡密,
                            "类型": 卡密类型
                        })
                        成功生成数量 += 1
                    
                    return 生成结果
                
                try:
                    # 执行事务
                    生成的卡密列表 = self.数据库.在事务中执行(生成卡密事务)
                    
                    if not 生成的卡密列表 or len(生成的卡密列表) != 数量: # 检查是否生成了请求的数量
                        return self._管理员接口响应(False, f"生成卡密失败，成功生成{len(生成的卡密列表)}个", 生成的卡密列表)
                    
                    return self._管理员接口响应(True, f"成功生成{len(生成的卡密列表)}个{卡密类型}卡密", 生成的卡密列表)
                    
                except Exception as e:
                    logging.error(f"生成卡密失败: {e}")
                    return self._管理员接口响应(False, f"生成失败: {str(e)}", [])
            
            # 处理GET请求（仅用于测试API是否可访问）
            return self._管理员接口响应(True, "卡密生成API可用", [])
                
        except Exception as e:
            logging.error(f"管理员生成卡密API错误: {e}")
            return self._管理员接口响应(False, f"服务器错误: {str(e)}", [])

    # 管理员卡密生成页面
    def admin_cards_page(self):
        """管理员卡密生成HTML页面，从外部文件读取"""
        try:
            # 从同一目录下读取HTML文件
            html_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "卡密管理工具.html")
            
            with open(html_file_path, "r", encoding="utf-8") as f:
                html_content = f.read()
                
            from flask import Response
            return Response(html_content, mimetype='text/html')
        except Exception as e:
            logging.error(f"读取卡密管理工具HTML文件失败: {e}")
            return f"<h1>错误</h1><p>无法加载卡密管理工具页面: {str(e)}</p>"
            
    # 管理员搜索卡密API端点
    def admin_search_cards(self):
        """管理员搜索卡密API端点，仅支持按卡密搜索"""
        try:
            # 处理跨域请求
            if request.method == 'OPTIONS':
                response = self._管理员接口响应(True, "跨域预检成功", [])
                return response
            
            # 处理POST请求
            if request.method == 'POST':
                data = request.get_json()
                if not data:
                    return self._管理员接口响应(False, "缺少请求数据", [])
                
                # 获取参数，只取卡密
                卡密 = data.get('card_key')
                
                if not 卡密:
                     return self._管理员接口响应(False, "缺少卡密参数", [])
                
                # 调用卡密管理器进行搜索 (使用简化的搜索方法)
                成功, 消息, 卡密列表 = self.卡密管理.搜索卡密按卡密(卡密)
                
                return self._管理员接口响应(成功, 消息, 卡密列表)
                
            return self._管理员接口响应(True, "卡密搜索API可用 (仅支持POST按卡密搜索)", []) # GET请求仅用于测试
            
        except Exception as e:
            logging.error(f"管理员搜索卡密API错误: {e}")
            return self._管理员接口响应(False, f"服务器错误: {str(e)}", [])

    # 管理员添加卡密时长API端点
    def admin_add_card_duration(self):
        """管理员为卡密添加时长API端点"""
        try:
            # 处理跨域请求
            if request.method == 'OPTIONS':
                response = self._管理员接口响应(True, "跨域预检成功", None)
                return response
                
            # 处理POST请求
            if request.method == 'POST':
                data = request.get_json()
                if not data:
                    return self._管理员接口响应(False, "缺少请求数据", None)
                
                # 验证必填参数
                卡密 = data.get('card_key')
                卡密类型 = data.get('card_type')
                数量 = data.get('quantity', 1) # 默认为1
                
                if not 卡密 or not 卡密类型:
                    return self._管理员接口响应(False, "缺少卡密或卡密类型参数", None)
                
                try:
                    数量 = int(数量)
                    if 数量 < 1:
                         return self._管理员接口响应(False, "数量必须大于等于1", None)
                except ValueError:
                    return self._管理员接口响应(False, "数量参数无效", None)
                
                # 调用卡密管理器添加时长
                成功, 消息, 更新后卡密信息 = self.卡密管理.添加卡密时长(卡密, 卡密类型, 数量)
                
                return self._管理员接口响应(成功, 消息, 更新后卡密信息)
                
            return self._管理员接口响应(True, "添加卡密时长API可用", None) # GET请求仅用于测试
            
        except Exception as e:
            logging.error(f"管理员添加卡密时长API错误: {e}")
            return self._管理员接口响应(False, f"服务器错误: {str(e)}", None)


def 注册管理员路由(app, 数据库实例=None, 卡密管理实例=None):
    """
    注册管理员相关的路由到Flask应用
    
    参数:
        app: Flask应用实例
        数据库实例: 数据库管理器实例（可选）
        卡密管理实例: 卡密管理器实例（可选）
    """
    # 使用蓝图注册路由，方便管理
    admin_bp = Blueprint('admin', __name__, url_prefix='/api/admin')
    admin_pages_bp = Blueprint('admin_pages', __name__, url_prefix='/admin')
    
    # 创建支付API处理实例，只传入管理员需要的依赖
    支付处理 = 支付API处理(数据库=数据库实例, 卡密管理=卡密管理实例)
    
    # 注册管理员API路由
    admin_bp.route('/generate_cards', methods=['POST', 'GET', 'OPTIONS'])(支付处理.admin_generate_cards) # 管理员批量生成卡密API
    admin_bp.route('/search_cards', methods=['POST', 'GET', 'OPTIONS'])(支付处理.admin_search_cards) # 管理员搜索卡密API
    admin_bp.route('/add_card_duration', methods=['POST', 'GET', 'OPTIONS'])(支付处理.admin_add_card_duration) # 管理员添加卡密时长API
    
    # 注册管理员页面路由
    admin_pages_bp.route('/cards', methods=['GET'])(支付处理.admin_cards_page) # 管理员卡密管理页面
    
    # 注册蓝图到应用
    app.register_blueprint(admin_bp)
    app.register_blueprint(admin_pages_bp)

    # 不再从这里返回支付处理实例，因为主程序不再直接调用其方法
    # return 支付处理

# 如果作为独立文件运行，可以进行测试
if __name__ == '__main__':
    from flask import Flask
    # 注意：独立运行时需要确保数据库和卡密管理器可用
    # 实际应用中，应该在主程序中初始化并传入
    app = Flask(__name__)
    db_manager = 数据库管理器()
    card_manager = 卡密管理器(db_manager)
    注册管理员路由(app, db_manager, card_manager)
    print("管理员路由已注册")
    print("管理员页面: http://127.0.0.1:5002/admin/cards")
    print("管理API:")
    print("- 生成卡密: http://127.0.0.1:5002/api/admin/generate_cards")
    print("- 查询卡密: http://127.0.0.1:5002/api/admin/search_cards")
    print("- 增加时长: http://127.0.0.1:5002/api/admin/add_card_duration")
    # 运行开发服务器
    app.run(host='0.0.0.0', port=5002, debug=True) 