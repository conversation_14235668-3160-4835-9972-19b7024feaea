<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor 常见问题解决方案</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --text-color: #2b2d42;
            --light-text: #4f5d75;
            --background: #f8f9fa;
            --card-bg: #ffffff;
            --warning-color: #ff9f1c;
            --error-color: #e71d36;
            --success-color: #2ec4b6;
            --border-radius: 12px;
            --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "PingFang SC", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.7;
            color: var(--text-color);
            background-color: var(--background);
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 0;
            background-color: transparent;
        }
        
        header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            box-shadow: var(--box-shadow);
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            z-index: 1;
        }
        
        header h1 {
            font-size: 2.4rem;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        .subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content-wrapper {
            padding: 0 20px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            margin-bottom: 30px;
            transition: var(--transition);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
            margin: 25px 0;
            position: relative;
        }
        
        h2, h3, h4 {
            color: var(--primary-color);
            margin: 0 0 20px 0;
            position: relative;
            display: inline-block;
        }
        
        h4 {
            font-size: 1.4rem;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        h4::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            border-radius: 3px;
        }
        
        p {
            margin-bottom: 15px;
            color: var(--light-text);
        }
        
        a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
        }
        
        a::after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--accent-color);
            transition: var(--transition);
        }
        
        a:hover {
            color: var(--accent-color);
        }
        
        a:hover::after {
            width: 100%;
        }
        
        img {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            margin: 20px 0;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: 1px solid rgba(0, 0, 0, 0.05);
            width: 450px;
            display: block;
            margin-left: auto;
            margin-right: auto;
            object-fit: contain;
        }
        
        img:hover {
            transform: scale(1.01);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .solution-card {
            background-color: rgba(67, 97, 238, 0.05);
            border-left: 4px solid var(--primary-color);
            padding: 20px;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            margin: 20px 0;
            transition: var(--transition);
        }
        
        .solution-card:hover {
            background-color: rgba(67, 97, 238, 0.08);
            transform: translateX(5px);
        }
        
        .solution-card ul, .solution-card ol {
            margin-left: 25px;
            margin-top: 15px;
        }
        
        .solution-card li {
            margin-bottom: 10px;
            position: relative;
        }
        
        .solution-card ol li::marker {
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .solution-card ul li::before {
            content: "•";
            color: var(--primary-color);
            font-weight: bold;
            position: absolute;
            left: -20px;
            top: 0;
        }
        
        .highlight-box {
            background-color: rgba(255, 159, 28, 0.1);
            border-left: 4px solid var(--warning-color);
            padding: 20px;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            margin: 25px 0;
            position: relative;
            transition: var(--transition);
        }
        
        .highlight-box:hover {
            background-color: rgba(255, 159, 28, 0.15);
        }
        
        .highlight-box strong {
            color: var(--warning-color);
        }
        
        .highlight-box::before {
            content: "\f071";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            position: absolute;
            color: var(--warning-color);
            font-size: 1.5rem;
            top: -15px;
            left: -15px;
            background-color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        code {
            display: block;
            background-color: #f1f5f9;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            overflow-x: auto;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 0.9rem;
            border: 1px solid rgba(0, 0, 0, 0.05);
            color: #334155;
            line-height: 1.5;
        }
        
        .website-link {
            text-align: center;
            font-size: 1.2rem;
            background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(76, 201, 240, 0.05));
            padding: 25px;
            border-radius: var(--border-radius);
            margin: 25px 0;
            border: 1px solid rgba(67, 97, 238, 0.1);
            transition: var(--transition);
        }
        
        .website-link:hover {
            background: linear-gradient(135deg, rgba(67, 97, 238, 0.08), rgba(76, 201, 240, 0.08));
            transform: translateY(-3px);
            box-shadow: var(--box-shadow);
        }
        
        .website-link strong {
            color: var(--primary-color);
        }
        
        .problem-section {
            position: relative;
            padding-top: 10px;
        }
        
        .problem-section::before {
            content: "";
            position: absolute;
            left: -30px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
            opacity: 0.3;
        }
        
        footer {
            text-align: center;
            margin-top: 50px;
            color: var(--light-text);
            font-size: 0.9rem;
            padding: 20px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .date-badge {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-top: 5px;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            opacity: 0;
            visibility: hidden;
            z-index: 100;
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            background-color: var(--secondary-color);
            transform: translateY(-5px);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            header {
                padding: 30px 20px;
                border-radius: 0;
            }
            
            header h1 {
                font-size: 1.8rem;
            }
            
            .card {
                padding: 20px;
                border-radius: var(--border-radius);
                margin-bottom: 20px;
            }
            
            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 40px;
                height: 40px;
            }
            
            img {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Cursor 常见问题解决方案</h1>
            <div class="subtitle">一站式解决您在使用过程中遇到的各种问题</div>
        </header>
        
        <div class="content-wrapper">
            <div class="card">
                <div class="website-link">
                    <strong>官方网站：</strong><a href="https://www.suiyuee.top" target="_blank">www.suiyuee.top</a>
                </div>
                
                <div class="highlight-box">
                    <strong>⚠️ 重要提示：</strong>杀毒软件可能影响软件正常运作，请卸载或添加白名单
                </div>
                
                <div class="divider"></div>
                
                <div class="problem-section">
                    <h4>通用方法: 一招解决70%以上的问题</h4>
                    <div class="solution-card">
                        <p>卸载杀毒软件，关掉梯子，下载<a href="https://www.123684.com/s/ukekTd-a0ol" target="_blank">卸载工具</a>，卸载cursor→重启电脑→<a href="https://www.cursor.com/cn" target="_blank">官网下载cursor</a>安装cursor</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h4>假弹窗Upgrade to Pro(请无视,关闭弹窗继续对话):</h4>
                <div class="solution-card">
                    <p>当看到升级提示弹窗时，这是假弹窗，直接关闭继续使用即可。</p>
                </div>
            </div>
            
            <div class="card">
                <div class="problem-section">
                    <h4>问题 1：Claude 服务器高负载 (High Load)</h4>
                    <code>
High Load 
We're experiencing high demand for Claude 3.7 Sonnet right now. Please upgrade to Pro, or switch to the
'default' model, Claude 3.5 sonnet, another model, or try again in a few moments.
                    </code>
                    
                    <p>Claude官方偶尔会出现高负载情况</p>
                    <div class="solution-card">
                        <ol>
                            <li>更换其他模型使用,更换claude3.5,或者谷歌geminmi模型或者chatgpt模型</li>
                            <li>更换其他模型使用尝试在非高峰时段访问</li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="problem-section">
                    <h4>问题2：User is unauthorized (用户未授权)</h4>
                    <p>提示"User is unauthorized"错误，带有RequestID编号</p>
                    <div class="solution-card">
                        <p>如果是个别情况，尝试再次获取一次额度或在售后群联系技术!如果所有人都出现,则等待技术修复后再获取一次即可</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="problem-section">
                    <h4>问题3：Connection failed (连接失败). lf the problem persists, please check your internet connection Or VPN</h4>
                    <p>提示"Connection failed"错误，建议检查网络连接或VPN</p>
                    
                    <p>此问题为个别情况：</p>
                    <div class="solution-card">
                        <ul>
                            <li>更换模型，不一定非要cladu模型，然后尝试对话</li>
                            <li>手动退出当前账号，关闭cursor，再点一键获取尝试后尝试对话</li>
                            <li>或者尝试换网络换节点 换全局，或关节点 关全局，或重启，或用<a href="https://www.123684.com/s/ukekTd-a0ol">卸载工具</a>卸载重装，或换<a href="https://downloader-cursor.deno.dev/">尝试其他版本</a>试试，或过些时间再试</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="problem-section">
                    <h4>问题4：Our servers are currently overloaded for non-pro users (官方服务器过载)</h4>
                    <p>提示"Our servers are currently overloaded for non-pro users"错误，表示服务器对免费用户过载</p>
                    
                    <div class="solution-card">
                        <p>这个模型对免费用户负载了，换个不同类型的模型就好了，不需要再获取额度</p>
                    </div>
                </div>
            </div>
            
            <footer>
                <p>内容更新时间:</p>
                <div class="date-badge" id="update-date"></div>
            </footer>
        </div>
    </div>
    
    <div class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </div>
    
    <script>
        // 自动更新日期
        document.getElementById('update-date').textContent = new Date().toLocaleDateString('zh-CN');
        
        // 返回顶部按钮
        const backToTopButton = document.getElementById('backToTop');
        
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });
        
        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // 添加卡片动画效果
        const cards = document.querySelectorAll('.card');
        
        const observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = 1;
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        cards.forEach(card => {
            card.style.opacity = 0;
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            observer.observe(card);
        });
        
        // 禁止复制内容
        document.addEventListener('contextmenu', event => event.preventDefault()); // 禁用右键菜单
        
        // 禁用选择
        document.addEventListener('selectstart', event => {
            // 允许表单元素选择文本
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                return true;
            }
            event.preventDefault();
            return false;
        });
        
        // 禁用复制
        document.addEventListener('copy', event => {
            // 允许表单元素复制文本
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                return true;
            }
            event.preventDefault();
            return false;
        });
        
        // 禁用剪切
        document.addEventListener('cut', event => {
            // 允许表单元素剪切文本
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                return true;
            }
            event.preventDefault();
            return false;
        });
        
        // 禁用键盘快捷键
        document.addEventListener('keydown', event => {
            // 允许表单元素使用键盘快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                return true;
            }
            
            // 禁用Ctrl+C, Ctrl+X, Ctrl+V, Ctrl+S, Ctrl+U, Ctrl+P, F12
            if (
                (event.ctrlKey && (
                    event.keyCode === 67 || // Ctrl+C
                    event.keyCode === 88 || // Ctrl+X
                    event.keyCode === 86 || // Ctrl+V
                    event.keyCode === 83 || // Ctrl+S
                    event.keyCode === 85 || // Ctrl+U
                    event.keyCode === 80    // Ctrl+P
                )) || 
                event.keyCode === 123       // F12
            ) {
                event.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>
