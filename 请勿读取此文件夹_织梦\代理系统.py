import os
import logging
import datetime
import mysql.connector
from flask import Flask, request, jsonify, Blueprint, Response

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class Config:
    """系统配置类"""
    # 数据库配置
    数据库配置 = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'YU6709',
        'database': 'kami3162'
    }
    
    # 服务配置
    服务配置 = {
        'host': '0.0.0.0',
        'port': 5103,
        'debug': True
    }

class 数据库管理器:
    """数据库连接和操作管理"""
    def __init__(self, 配置=None):
        # 尝试使用Config中的数据库配置，如果提供了配置则使用提供的
        self.配置 = 配置 or Config.数据库配置

    def 获取连接(self):
        """获取数据库连接"""
        try:
            # 添加错误处理，避免连接失败导致程序崩溃
            return mysql.connector.connect(**self.配置)
        except mysql.connector.Error as err:
            logging.error(f"数据库连接失败: {err}")
            # 可以选择重新抛出异常或返回 None/特殊值
            raise err

    def 执行查询(self, sql, 参数=None):
        """执行查询并返回结果"""
        conn = None
        cursor = None
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchall()
            return result
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn and conn.is_connected():
                conn.close()

    def 执行单条查询(self, sql, 参数=None):
        """执行查询并返回单条结果"""
        conn = None
        cursor = None
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchone()
            return result
        except Exception as e:
            logging.error(f"单条查询执行失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn and conn.is_connected():
                conn.close()

class 代理系统:
    """代理系统类，处理代理秘钥查询和相关订单显示"""
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
    
    def 查询代理秘钥(self, 代理秘钥):
        """
        查询代理秘钥是否存在以及对应的代理名称
        
        参数:
            代理秘钥: 要查询的代理秘钥
            
        返回:
            (成功状态, 消息, 代理名称, 分成比例, 屏蔽年卡)
        """
        try:
            # 查询代理秘钥表，检查秘钥是否存在，同时获取内购分成比例和屏蔽年卡设置
            查询SQL = """
                SELECT 代理秘钥, 代理名字, 内购分成比例, 屏蔽年卡 
                FROM 代理秘钥 
                WHERE 代理秘钥 = %s 
                LIMIT 1
            """
            结果 = self.数据库.执行单条查询(查询SQL, [代理秘钥])
            
            if not 结果:
                return False, "代理秘钥不存在", None, None, None
                
            代理名称 = 结果.get('代理名字')
            分成比例 = 结果.get('内购分成比例')
            屏蔽年卡 = 结果.get('屏蔽年卡')
            
            if not 代理名称:
                return False, "代理名称未设置", None, None, None
                
            # 如果数据库中没有设置分成比例，默认使用0.5（50%）
            if 分成比例 is None:
                分成比例 = 0.5
            else:
                分成比例 = float(分成比例)
                
            # 如果数据库中没有设置屏蔽年卡，默认不屏蔽（0）
            if 屏蔽年卡 is None:
                屏蔽年卡 = 0
                
            return True, "查询成功", 代理名称, 分成比例, 屏蔽年卡
            
        except Exception as e:
            logging.error(f"查询代理秘钥失败: {e}")
            return False, f"查询失败: {str(e)}", None, None, None
    
    def 查询代理卡密列表(self, 代理名称, 屏蔽年卡=0):
        """
        根据代理名称查询对应的卡密订单列表
        
        参数:
            代理名称: 代理的名称
            屏蔽年卡: 是否屏蔽年卡类型（0不屏蔽，1屏蔽）
            
        返回:
            (成功状态, 消息, 订单列表)
        """
        try:
            查询参数 = [代理名称]
            
            # 根据屏蔽年卡参数决定是否过滤年卡类型
            if 屏蔽年卡 == 1:
                # 屏蔽年卡
                查询SQL = """
                    SELECT 订单号, 卡密, 卡密类型 as 类型, 支付时间, 金额
                    FROM 支付订单
                    WHERE 代理名字 = %s
                    AND 卡密类型 NOT LIKE %s
                    AND 卡密类型 NOT LIKE %s
                    ORDER BY 支付时间 DESC
                """
                查询参数.extend(['%年卡%', '%续卡-年卡%'])
            else:
                # 不屏蔽年卡
                查询SQL = """
                    SELECT 订单号, 卡密, 卡密类型 as 类型, 支付时间, 金额
                    FROM 支付订单
                    WHERE 代理名字 = %s
                    ORDER BY 支付时间 DESC
                """
            
            结果 = self.数据库.执行查询(查询SQL, 查询参数)
            
            if not 结果:
                return True, "未找到该代理的订单记录", []
                
            return True, "查询成功", 结果
            
        except Exception as e:
            logging.error(f"查询代理订单列表失败: {e}")
            return False, f"查询失败: {str(e)}", []
    
    def 计算代理分成金额(self, 代理名称, 时间段, 分成比例=0.5, 屏蔽年卡=0):
        """
        根据代理名称和时间段计算代理的分成金额
        
        参数:
            代理名称: 代理的名称
            时间段: 'today', 'yesterday', 'this_week', 'last_week', 'this_month'
            分成比例: 代理的分成比例，默认为0.5（50%）
            屏蔽年卡: 是否屏蔽年卡类型（0不屏蔽，1屏蔽）
            
        返回:
            (成功状态, 消息, 分成金额)
        """
        try:
            # 获取当前时间和时间区间
            当前时间 = datetime.datetime.now()
            开始时间 = None
            结束时间 = 当前时间
            
            # 根据不同时间段设置开始时间
            if 时间段 == 'today':  # 今天
                开始时间 = 当前时间.replace(hour=0, minute=0, second=0, microsecond=0)
            elif 时间段 == 'yesterday':  # 昨天
                # 昨天的开始时间（昨天0点）
                昨天 = 当前时间 - datetime.timedelta(days=1)
                开始时间 = 昨天.replace(hour=0, minute=0, second=0, microsecond=0)
                # 昨天的结束时间（昨天23:59:59）
                结束时间 = 昨天.replace(hour=23, minute=59, second=59, microsecond=999999)
            elif 时间段 == 'this_week':  # 本周（星期一到现在）
                # 计算本周一的日期
                周一偏移 = 当前时间.weekday()  # 0是周一，6是周日
                开始时间 = (当前时间 - datetime.timedelta(days=周一偏移)).replace(hour=0, minute=0, second=0, microsecond=0)
            elif 时间段 == 'last_week':  # 上周（上周一到上周日）
                # 计算上周一的日期
                上周一偏移 = 当前时间.weekday() + 7
                开始时间 = (当前时间 - datetime.timedelta(days=上周一偏移)).replace(hour=0, minute=0, second=0, microsecond=0)
                # 计算上周日的日期
                上周日偏移 = 当前时间.weekday() + 1
                结束时间 = (当前时间 - datetime.timedelta(days=上周日偏移)).replace(hour=23, minute=59, second=59, microsecond=999999)
            elif 时间段 == 'this_month':  # 本月（从1号到现在）
                开始时间 = 当前时间.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            else:
                return False, "无效的时间段参数", 0
            
            查询参数 = [代理名称, 开始时间, 结束时间]
            
            # 根据屏蔽年卡参数决定是否过滤年卡类型
            if 屏蔽年卡 == 1:
                # 屏蔽年卡
                查询SQL = """
                    SELECT SUM(金额) as 总金额
                    FROM 支付订单
                    WHERE 代理名字 = %s 
                    AND 支付时间 >= %s 
                    AND 支付时间 <= %s
                    AND 卡密类型 NOT LIKE %s
                    AND 卡密类型 NOT LIKE %s
                """
                查询参数.extend(['%年卡%', '%续卡-年卡%'])
            else:
                # 不屏蔽年卡
                查询SQL = """
                    SELECT SUM(金额) as 总金额
                    FROM 支付订单
                    WHERE 代理名字 = %s 
                    AND 支付时间 >= %s 
                    AND 支付时间 <= %s
                """
            
            # 执行查询
            结果 = self.数据库.执行单条查询(查询SQL, 查询参数)
            
            # 处理结果
            总金额 = 结果.get('总金额') if 结果 and 结果.get('总金额') else 0
            分成金额 = float(总金额) * 分成比例  # 使用传入的分成比例
            
            return True, "查询成功", 分成金额
            
        except Exception as e:
            logging.error(f"计算代理分成金额失败: {e}")
            return False, f"计算失败: {str(e)}", 0
    
    def 查询代理系统页面(self):
        """返回代理系统HTML页面"""
        try:
            # 从同一目录下读取HTML文件
            html_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "代理系统.html")
            
            with open(html_file_path, "r", encoding="utf-8") as f:
                html_content = f.read()
                
            return Response(html_content, mimetype='text/html')
        except Exception as e:
            logging.error(f"读取代理系统HTML文件失败: {e}")
            return f"<h1>错误</h1><p>无法加载代理系统页面: {str(e)}</p>"

class 代理API处理:
    """代理API处理类，提供代理系统相关API"""
    def __init__(self, 数据库=None, 代理系统=None):
        self.数据库 = 数据库 or 数据库管理器()
        self.代理系统 = 代理系统 or 代理系统(self.数据库)
        
    def _接口响应(self, 成功状态, 消息, 数据=None):
        """生成标准格式的接口响应"""
        response = jsonify({
            'success': 成功状态,
            'message': 消息,
            **(数据 if isinstance(数据, dict) else {'data': 数据})
        })
        # 允许跨域访问
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        return response
        
    def 检查代理秘钥(self):
        """检查代理秘钥API，接收POST请求"""
        try:
            # 处理跨域请求
            if request.method == 'OPTIONS':
                return self._接口响应(True, "跨域预检成功")
                
            # 处理POST请求
            if request.method == 'POST':
                data = request.get_json()
                if not data:
                    return self._接口响应(False, "缺少请求数据")
                
                # 获取代理秘钥参数
                代理秘钥 = data.get('agent_key')
                
                if not 代理秘钥:
                    return self._接口响应(False, "缺少代理秘钥参数")
                
                # 查询代理秘钥
                成功, 消息, 代理名称, 分成比例, 屏蔽年卡 = self.代理系统.查询代理秘钥(代理秘钥)
                
                if not 成功:
                    return self._接口响应(False, 消息)
                
                # 查询对应的卡密列表
                卡密成功, 卡密消息, 卡密列表 = self.代理系统.查询代理卡密列表(代理名称, 屏蔽年卡)
                
                if not 卡密成功:
                    return self._接口响应(False, 卡密消息)
                
                # 返回结果，增加分成比例和屏蔽年卡信息
                return self._接口响应(True, "查询成功", {
                    'agent_name': 代理名称,
                    'commission_rate': 分成比例,
                    'block_yearly_cards': 屏蔽年卡,
                    'cards': 卡密列表
                })
                
            # 处理GET请求（仅用于测试API是否可访问）
            return self._接口响应(True, "代理秘钥检查API可用")
                
        except Exception as e:
            logging.error(f"检查代理秘钥API错误: {e}")
            return self._接口响应(False, f"服务器错误: {str(e)}")

    def 查询代理分成(self):
        """查询代理分成金额API，接收POST请求"""
        try:
            # 处理跨域请求
            if request.method == 'OPTIONS':
                return self._接口响应(True, "跨域预检成功")
                
            # 处理POST请求
            if request.method == 'POST':
                data = request.get_json()
                if not data:
                    return self._接口响应(False, "缺少请求数据")
                
                # 获取参数
                代理秘钥 = data.get('agent_key')
                时间段 = data.get('period')
                
                if not 代理秘钥:
                    return self._接口响应(False, "缺少代理秘钥参数")
                    
                if not 时间段:
                    return self._接口响应(False, "缺少时间段参数")
                
                # 查询代理秘钥
                成功, 消息, 代理名称, 分成比例, 屏蔽年卡 = self.代理系统.查询代理秘钥(代理秘钥)
                
                if not 成功:
                    return self._接口响应(False, 消息)
                
                # 计算分成金额，传入分成比例和屏蔽年卡设置
                分成成功, 分成消息, 分成金额 = self.代理系统.计算代理分成金额(代理名称, 时间段, 分成比例, 屏蔽年卡)
                
                if not 分成成功:
                    return self._接口响应(False, 分成消息)
                
                # 返回结果，增加分成比例和屏蔽年卡信息
                return self._接口响应(True, "查询成功", {
                    'agent_name': 代理名称,
                    'period': 时间段,
                    'commission_rate': 分成比例,
                    'block_yearly_cards': 屏蔽年卡,
                    'commission_amount': 分成金额
                })
                
            # 处理GET请求（仅用于测试API是否可访问）
            return self._接口响应(True, "代理分成查询API可用")
                
        except Exception as e:
            logging.error(f"查询代理分成API错误: {e}")
            return self._接口响应(False, f"服务器错误: {str(e)}")

def 注册代理系统路由(app, 数据库实例=None):
    """
    注册代理系统相关的路由到Flask应用
    
    参数:
        app: Flask应用实例
        数据库实例: 数据库管理器实例（可选）
    """
    # 使用蓝图注册路由
    agent_bp = Blueprint('agent', __name__, url_prefix='/api/agent')
    agent_pages_bp = Blueprint('agent_pages', __name__, url_prefix='/agent')
    
    # 创建实例
    代理系统实例 = 代理系统(数据库实例)
    代理API = 代理API处理(数据库实例, 代理系统实例)
    
    # 注册API路由
    agent_bp.route('/check_key', methods=['POST', 'GET', 'OPTIONS'])(代理API.检查代理秘钥)
    agent_bp.route('/commission', methods=['POST', 'GET', 'OPTIONS'])(代理API.查询代理分成)
    
    # 注册页面路由
    agent_pages_bp.route('/', methods=['GET'])(代理系统实例.查询代理系统页面)
    
    # 注册蓝图到应用
    app.register_blueprint(agent_bp)
    app.register_blueprint(agent_pages_bp)

# 如果作为独立文件运行，可以进行测试
if __name__ == '__main__':
    from flask import Flask
    app = Flask(__name__)
    db_manager = 数据库管理器()
    
    # 检查必要的表是否存在
    def 检查数据库表():
        conn = None
        cursor = None
        try:
            conn = db_manager.获取连接()
            cursor = conn.cursor()
            
            # 检查代理秘钥表是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = %s 
                AND table_name = %s
            """, (Config.数据库配置['database'], '代理秘钥'))
            
            代理秘钥表存在 = cursor.fetchone()[0] > 0
            
            # 检查支付订单表是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = %s 
                AND table_name = %s
            """, (Config.数据库配置['database'], '支付订单'))
            
            支付订单表存在 = cursor.fetchone()[0] > 0
            
            if not 代理秘钥表存在:
                print("警告: '代理秘钥'表不存在，请先创建表")
                print("表结构: 代理秘钥(varchar), 代理名字(varchar), 创建时间(datetime)")
                return False
                
            if not 支付订单表存在:
                print("警告: '支付订单'表不存在，请先创建表")
                print("表结构: 订单号(varchar), 交易号(varchar), 金额(decimal), 机器码(varchar), 程序名(varchar), 卡密类型(varchar), 卡密(varchar), 支付时间(datetime), 有效期(datetime), 代理名字(varchar)")
                return False
                
            print("数据库表检查完成，所需表已存在")
            return True
            
        except Exception as e:
            logging.error(f"检查数据库表失败: {e}")
            print(f"检查数据库表出错: {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn and conn.is_connected():
                conn.close()
    
    # 检查数据库表
    表存在 = 检查数据库表()
    
    if 表存在:
        # 注册路由
        注册代理系统路由(app, db_manager)
        
        print("代理系统路由已注册")
        print(f"代理系统页面: http://127.0.0.1:{Config.服务配置['port']}/agent/")
        print("代理API:")
        print(f"- 检查代理秘钥: http://127.0.0.1:{Config.服务配置['port']}/api/agent/check_key")
        print(f"- 查询代理分成: http://127.0.0.1:{Config.服务配置['port']}/api/agent/commission")
        print("请确保数据库中已有代理秘钥和对应的卡密数据")
        
        # 运行开发服务器
        app.run(
            host=Config.服务配置['host'], 
            port=Config.服务配置['port'], 
            debug=Config.服务配置['debug']
        )
    else:
        print("程序退出：无法启动，请确保数据库中已创建必要的表") 