#!/bin/bash
echo "检查服务状态..."
echo "=========================="

# 检查端口监听状态
echo "端口监听状态:"
netstat -tulpn | grep -E '5001|5269|5270' || echo "未发现服务端口监听"

echo ""
echo "进程运行状态:"
# 检查进程是否运行
ps aux | grep -E "(支付系统|免费限制|付费限制|邮箱监控)" | grep -v grep || echo "未发现相关进程"

echo ""
echo "最新日志 (最后10行):"
echo "--- 支付系统日志 ---"
if [ -f "/opt/支付系统.log" ]; then
    tail -10 /opt/支付系统.log
else
    echo "日志文件不存在"
fi

echo ""
echo "--- 免费限制日志 ---"
if [ -f "/opt/免费限制.log" ]; then
    tail -10 /opt/免费限制.log
else
    echo "日志文件不存在"
fi

echo ""
echo "--- 付费限制日志 ---"
if [ -f "/opt/付费限制.log" ]; then
    tail -10 /opt/付费限制.log
else
    echo "日志文件不存在"
fi

echo ""
echo "--- 邮箱监控日志 ---"
if [ -f "/opt/邮箱监控.log" ]; then
    tail -10 /opt/邮箱监控.log
else
    echo "日志文件不存在"
fi
