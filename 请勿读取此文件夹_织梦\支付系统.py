#服务端 避免冗余
import json
import random
import time
import datetime
import logging
import webbrowser
import string
import mysql.connector
from alipay.aop.api.AlipayClientConfig import AlipayClientConfig
from alipay.aop.api.DefaultAlipayClient import DefaultAlipayClient
from alipay.aop.api.request.AlipayTradePagePayRequest import AlipayTradePagePayRequest
from alipay.aop.api.request.AlipayTradeQueryRequest import AlipayTradeQueryRequest
from alipay.aop.api.request.AlipayTradeRefundRequest import AlipayTradeRefundRequest
from alipay.aop.api.util.SignatureUtils import verify_with_rsa
from flask import request, jsonify
import os

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class Config:
    """系统配置类"""
    # 支付宝配置
    支付宝配置 = {
        'app_id': "2021005117695116",
        'private_key': """MIIEowIBAAKCAQEAhA05tqs6Yv8d0mh1y0bJm9XPXWxKSMXPO4jTBkcL9S2bJT+5PrSKe6Ux/YUCEcoJ7jhVOkduau71BbqyaQmNKmYugta5KZttmLzaZW9cQeao6xrNcA2IxcgMkOZABCJdMyApmIqcQWMIIoF+aaPV72pPLmBYlgmLoYBG6OPnA3BOex1Y728AJL4rs5RT4bNminamUY2E8y5NTdwv+IahkUsYdLVqH1e3fl+HL10Q82yQh3s1G3M65J9Ny5YYFHLRciud6/9J8K0OPadLBw71+ZOfuz/70UC7IW6VGQVkopsABAVKaMqdwzmHFeYIC2mohc8vCmJ49wJBfVqzgaWsyQIDAQABAoIBADzReotPQYzmwu1tfxMofyJuzxrQDUaHFHLvCYISpsc9Qf9d4gunS0C3TNbes51N2FUHTPmSfKBzfhYtbo5B/+pPNYy9KHqpQ0jdchjVuUAA5DukFIpPMXcmM+NoqIM+8H3jikTfUigrXEvIKFivRJEH/X/2j9ZKfyzaz83jaTqDhInQFkrLDcI2cmECJJD+s+G/DpNGwOKPtydpYgK7AS1loVCSxTbVQytgiO+NrJuunR4KeOiN+SxKFMcb6OgRrclli1IWYM+C7xp9sAI1PBSjSXPW8pTJQ0FpMKrOfSGMhwWcS6S9DgIuKLXakJWUoTAnFLMkY1RjTjz7Z/t03aECgYEA/2izFXWOhK8InUgQsjIf+QH/opCbM0WnCjOSv3skq67sti5qvjmamIMw/dfn00KWHy3FzrtxDQja1G16Gkvdf/bV6UIBJo4u5wR9biXderahWH+V4HKRIjg8IiLILWZWjGgPLjiuU8epOeB0+20P/gpxJKKA2gdjgfzeQYHTvBsCgYEAhFtzbCl2bV69dLWRj78nXlSrhzpr8JKWLLgrpBbRZ96lo0plWwGJVYcO4a6oXW7tb/Q1K9y6LNJPBAtCDitrZ6SPRVMP0pe5Q0R0NK3alFmugwRLiWRI8GpYxFk9fNmH4+I58zKMFVZP9G0efQuuWpTPGza/kqYDjTjjzrFYAOsCgYBD4YXHLElGhmaZtq+JTL0mpxtUD27QP7DXi0FzVr02S/Vprgh8qj6mTifVL27TvRy9CKNMAJFZT4Mpdjc/ie5Ctk3AAuTfZGaey5iyj6m/lY5oQPCKiKh+GcgRooDlYA+cnYN7W2EpryUQ1tEAicn4ULolbHMmf1Q5FgXUDzdBBQKBgE0Eyk2OcxOBpc1cOq/jFA2YR5gRLu523lx3by20+XXv4YL7r68dXcUfKlG5SM/V1OTovmgfsVaTqtc3leCY848D+zrj8rkhAPVbyCM/Z4ocBajHvwzcBnWf7LeVFmqoSqMY6Y/TQiDIcQ5S0QMC4RpkrX+whBnZ8Qckq6uDFyqxAoGBAIC8aBSiw8LCgguPzJ0lqu8avyHWhxtREhGk8PUH5No3Rk8iAJZte87VNgBrcS16EYmggURYXtRI1df5+qYeURhQyGvBe2RNUssFGY9dMWLFPUM6g9wGytlXL8+LLIADDO2RMXJgecT6K/qtoLJ3wzKwOCS+7giR1JKT9JJku3Tn""",
        'alipay_public_key': """MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkPqSGRasiee10yKOG8pyCBRvBcjAIPmS4A4fbSYtyFUEv30VNzIbQymlZf70T+uIY6qQsscMdZbEnmbiVyWBvO42641DVCmvN+1brdnV0yhgtFPBYELgddvxPvcfIMeeeStDEzfTjuGHYFeMmChcjki7aA44B3ooN+LKUH4NkBXD7fxEGcEueZssiy90ZlN/6x0K6YYLMOtUcpcyVsqCHa8k9N/1Jdr49AldK8BBYkc3x9kz0bw11tgKe1aJG1Q+TqLoGv+SstPcOh2CBM2M4t153TNAupJXUg4U/UvkGlla5DOHTPnJzRht8hkSYsdaKLdJ8+00sfxQcqpM7U5q0wIDAQAB""",
        'app_name': "卡密验证系统",
        'return_url': "http://***************:5001/payment_result", # 支付宝回调地址
        'notify_url': "http://***************:5001/alipay_notify" # 支付宝异步通知地址
    }
    
    # 卡密类型配置
    卡密类型 = {
        '有效类型': ['时卡', '天卡', '周卡', '月卡', '季卡', '年卡'],
        '类型价格': {
            '时卡': 2.0,
            '天卡': 6.6,
            '周卡': 26.9,
            '月卡': 45.9,
            '季卡': 89.9,
            '年卡': 298.0
        }
    }
    
    # 数据库配置
    数据库配置 = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'YU6709',
        'database': 'kami3162'
    }
    
    # 服务配置
    服务配置 = {
        'host': '0.0.0.0',     # 绑定的IP地址，0.0.0.0表示所有网卡
        'port': 5001,          # 服务端口
        'debug': False,         # 是否启用调试模式
        'public_host': '***************',  # 公网IP或域名
        'public_port': 5001    # 公网端口
    }
    
    @classmethod
    def get_public_url(cls):
        """获取公网URL，用于支付回调等场景"""
        return f"http://{cls.服务配置['public_host']}:{cls.服务配置['public_port']}"

class 数据库管理器:
    """数据库连接和操作管理"""
    def __init__(self, 配置=None):
        self.配置 = 配置 or Config.数据库配置

    def 获取连接(self):
        """获取数据库连接"""
        return mysql.connector.connect(**self.配置)

    def 执行查询(self, sql, 参数=None):
        """执行查询并返回结果"""
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchall()
            cursor.close()
            conn.close()
            return result
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise

    def 执行单条查询(self, sql, 参数=None):
        """执行查询并返回单条结果"""
        try:
            conn = self.获取连接()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, 参数 or ())
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            return result
        except Exception as e:
            logging.error(f"单条查询执行失败: {e}")
            raise

    def 执行更新(self, sql, 参数=None):
        """执行更新操作"""
        try:
            conn = self.获取连接()
            cursor = conn.cursor()
            cursor.execute(sql, 参数 or ())
            conn.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            conn.close()
            return affected_rows
        except Exception as e:
            logging.error(f"更新执行失败: {e}")
            raise
            
    def 在事务中执行(self, 回调函数):
        """
        在事务中执行一系列数据库操作
        
        参数:
            回调函数: 接收连接和游标的函数，在其中执行SQL操作
        
        返回:
            回调函数的返回值
        """
        conn = None
        try:
            conn = self.获取连接()
            conn.start_transaction()  # 开始事务
            cursor = conn.cursor(dictionary=True)
            
            # 执行回调函数，传入连接和游标
            result = 回调函数(conn, cursor)
            
            # 提交事务
            conn.commit()
            return result
        except Exception as e:
            # 发生异常，回滚事务
            if conn:
                conn.rollback()
            logging.error(f"事务执行失败: {e}")
            raise
        finally:
            # 确保关闭游标和连接
            if conn:
                if conn.is_connected():
                    cursor.close()
                    conn.close()

class 卡密管理器:
    """卡密管理类，提供卡密相关功能"""
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
        self.卡密类型配置 = Config.卡密类型
    
    def _计算到期时间(self, 卡密类型, 当前时间=None):
        """
        根据卡密类型计算到期时间

        参数:
            卡密类型: 卡密类型（时卡、天卡、周卡、月卡、季卡、年卡）
            当前时间: 当前时间，默认为None，将使用当前系统时间

        返回:
            到期时间对象和格式化的到期时间字符串
        """
        当前时间 = 当前时间 or datetime.datetime.now()
        
        到期时间映射 = {
            "时卡": datetime.timedelta(hours=1),
            "天卡": datetime.timedelta(days=1),
            "周卡": datetime.timedelta(days=7),
            "月卡": datetime.timedelta(days=30),
            "季卡": datetime.timedelta(days=90),
            "年卡": datetime.timedelta(days=365)
        }
        
        到期时间 = 当前时间 + 到期时间映射.get(卡密类型, datetime.timedelta(days=1))
        到期时间字符串 = 到期时间.strftime('%Y-%m-%d %H:%M:%S')
        
        return 到期时间, 到期时间字符串
    
    def _生成随机卡密(self, 长度=10):
        """
        生成随机卡密
        
        参数:
            长度: 卡密长度，默认10位
            
        返回:
            生成的卡密
        """
        字符集 = string.ascii_letters + string.digits
        卡密 = ''.join(random.choice(字符集) for _ in range(长度))
        return 卡密

    def _验证卡密字符串(self, 卡密):
        """
        验证卡密字符串格式是否有效
        
        参数:
            卡密: 要验证的卡密字符串
            
        返回:
            bool: 卡密格式是否有效
        """
        # 检查基本长度要求
        if not 卡密 or len(卡密) < 4:
            return False
            
        # 直接检查是否全部是字母和数字
        return all(c in string.ascii_letters + string.digits for c in 卡密)

    def 查询卡密状态(self, 卡密):
        """
        查询卡密状态
        
        参数:
            卡密: 要查询的卡密
            
        返回:
            (成功状态, 消息, 卡密数据)
        """
        try:
            # 查询卡密信息
            查询SQL = """
                SELECT 卡密, 类型, 机器码, 首次登录时间, 最近登录, 到期时间, 本月登录次数, 最大数量, 备注
                FROM 卡密系统
                WHERE 卡密 = %s
                LIMIT 1
            """
            结果 = self.数据库.执行单条查询(查询SQL, [卡密])
            
            if not 结果:
                return False, "卡密不存在", None
                
            return True, "查询成功", 结果
            
        except Exception as e:
            logging.error(f"查询卡密状态失败: {e}")
            return False, f"查询失败: {str(e)}", None

class 支付宝支付系统:
    """支付宝支付系统类"""
    def __init__(self, 配置=None, 数据库=None):
        self.配置 = 配置 or Config.支付宝配置
        self.app_name = self.配置.get('app_name')
        self.卡密类型配置 = Config.卡密类型
        self.数据库 = 数据库 or 数据库管理器()

        # 初始化支付宝客户端配置
        self._config = AlipayClientConfig()
        self._config.app_id = self.配置.get('app_id')
        self._config.app_private_key = self.配置.get('private_key')
        self._config.alipay_public_key = self.配置.get('alipay_public_key')
        self._config.sign_type = "RSA2"
        self._config.charset = "utf-8"

        self._client = DefaultAlipayClient(alipay_client_config=self._config)

    def 获取卡片价格(self, 卡密类型):
        """
        从数据库获取卡片价格，如果数据库中没有则使用默认价格

        参数:
            卡密类型: 卡片类型

        返回:
            价格（float）
        """
        try:
            # 从cursorpro表查询价格信息
            # 注意：支付系统没有缓存机制，每次都直接查询数据库获取最新价格
            价格配置 = self.数据库.执行单条查询(
                "SELECT 时卡, 天卡, 周卡, 月卡, 季卡, 年卡 FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            # 字段名映射
            字段映射 = {
                '时卡': '时卡',
                '天卡': '天卡',
                '周卡': '周卡',
                '月卡': '月卡',
                '季卡': '季卡',
                '年卡': '年卡'
            }

            # 优先使用数据库中的价格
            if 价格配置 and 卡密类型 in 字段映射:
                字段名 = 字段映射[卡密类型]
                if 字段名 in 价格配置 and 价格配置[字段名] is not None:
                    return float(价格配置[字段名])

            # 如果数据库中没有，使用默认价格
            默认价格 = self.卡密类型配置['类型价格'].get(卡密类型)
            if 默认价格 is not None:
                return float(默认价格)

            # 如果都没有，返回0
            logging.warning(f"未找到卡密类型 {卡密类型} 的价格配置")
            return 0.0

        except Exception as e:
            logging.error(f"获取卡片价格失败: {e}")
            # 出错时使用默认价格
            默认价格 = self.卡密类型配置['类型价格'].get(卡密类型, 0.0)
            return float(默认价格)

    def 创建支付页面(self, 订单号: str, 总金额: float, 商品名称: str = None, 机器码: str = None, 程序名: str = None, 卡密类型: str = None, 备注: str = None, 卡密: str = None, 是否续卡: bool = False):
        """
        创建支付页面
        
        参数:
            订单号: 订单号
            总金额: 支付金额
            商品名称: 商品名称，默认为app_name
            机器码: 机器码（可选，用于回传）
            程序名: 程序名（可选，用于回传）
            卡密类型: 卡密类型（可选，用于回传）
            备注: 备注信息（可选，用于回传）
            卡密: 卡密（续卡时需要，用于回传）
            是否续卡: 是否为续卡订单
        
        返回:
            支付宝支付链接
        """
        try:
            商品名称 = 商品名称 or self.app_name
            
            request = AlipayTradePagePayRequest()
            # 设置同步跳转地址
            request.return_url = self.配置.get('return_url')
            # 设置异步通知地址
            request.notify_url = self.配置.get('notify_url')
            
            # 创建业务参数字典
            biz_content = {
                "out_trade_no": 订单号,
                "total_amount": f"{总金额:.2f}",
                "subject": 商品名称,
                "product_code": "FAST_INSTANT_TRADE_PAY",
                "qr_pay_mode": "2"
            }
            
            # 添加回传参数
            passback_params = ""
            if 机器码:
                passback_params = f"machine_code={机器码}"
            if 程序名:
                if passback_params:
                    passback_params += f"&app_name={程序名}"
                else:
                    passback_params = f"app_name={程序名}"
            
            # 添加卡密类型到回传参数中
            if 卡密类型:
                if passback_params:
                    passback_params += f"&card_type={卡密类型}"
                else:
                    passback_params = f"card_type={卡密类型}"
            
            # 添加备注到回传参数中
            if 备注:
                if passback_params:
                    passback_params += f"&remark={备注}"
                else:
                    passback_params = f"remark={备注}"
            
            # 添加卡密到回传参数中（续卡时需要）
            if 卡密:
                if passback_params:
                    passback_params += f"&card_key={卡密}"
                else:
                    passback_params = f"card_key={卡密}"
            
            # 添加是否续卡标志
            if 是否续卡:
                if passback_params:
                    passback_params += f"&is_renewal=1"
                else:
                    passback_params = f"is_renewal=1"
                    
            if passback_params:
                import urllib.parse
                biz_content["passback_params"] = urllib.parse.quote(passback_params)
            
            # 设置支付业务参数
            request.biz_content = biz_content

            response = self._client.sdk_execute(request)
            if not response:
                return ""

            # 使用支付宝开放平台上设置的网关
            full_url = f"https://openapi.alipay.com/gateway.do?{response}"

            return full_url
        except Exception as e:
            logging.error(f"创建支付时出错: {e}")
            return ""
    
    def 验证支付宝通知(self, 数据):
        """验证支付宝异步通知的真实性"""
        try:
            # 创建数据副本，避免修改原始数据
            数据副本 = 数据.copy()
            
            # 获取签名
            签名 = 数据副本.pop('sign', None)
            # 获取签名类型
            签名类型 = 数据副本.pop('sign_type', None)
            
            if not 签名 or 签名类型 != 'RSA2':
                return False
            
            # 按照支付宝要求，将参数排序并拼接成字符串
            ordered_items = sorted(数据副本.items())
            message = '&'.join(f"{k}={v}" for k, v in ordered_items)
            
            # 使用支付宝公钥验证签名
            result = verify_with_rsa(self.配置.get('alipay_public_key'), message.encode('utf-8'), 签名)
            return result
        except Exception as e:
            logging.error(f"验证签名出错: {e}")
            return False
            
    def 生成支付链接(self, 程序名: str = None, 卡密类型: str = None, 机器码: str = None, 备注: str = "官方版"):
        """
        根据程序名、卡密类型和机器码生成支付链接
        
        参数:
            程序名: 程序名称
            卡密类型: 卡密类型，必须是有效的类型（时卡、天卡、周卡、月卡、季卡、年卡）
            机器码: 机器码（可选）
            备注: 备注信息（可选）
        
        返回:
            dict: 包含成功状态、订单号、支付链接、金额和卡密类型的字典
        """
        try:
            # 验证卡密类型是否有效
            有效类型 = self.卡密类型配置['有效类型']
            if 卡密类型 not in 有效类型:
                return {"success": False, "message": f"无效的卡密类型，有效类型: {', '.join(有效类型)}"}
            
            # 从数据库获取金额
            金额 = self.获取卡片价格(卡密类型)
            if 金额 <= 0:
                return {"success": False, "message": f"卡密类型'{卡密类型}'没有设置价格"}
            
            # 生成订单号（直接内联生成，不再使用单独的函数）
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            random_num = str(random.randint(100, 999))
            订单号 = f"{timestamp}{random_num}"
            
            # 设置商品名称，简化为只显示卡密类型
            商品名称 = 卡密类型
            
            # 创建支付链接
            支付链接 = self.创建支付页面(订单号, 金额, 商品名称, 机器码, 程序名, 卡密类型, 备注)
            
            if not 支付链接:
                return {"success": False, "message": "创建支付链接失败"}
            
            return {
                "success": True,
                "order_no": 订单号,
                "payment_url": 支付链接,
                "amount": 金额,
                "card_type": 卡密类型,
                "remark": 备注
            }
        except Exception as e:
            logging.error(f"生成支付链接时出错: {e}")
            return {"success": False, "message": f"生成支付链接时出错: {str(e)}"}

    def 生成续卡支付链接(self, 卡密: str, 卡密类型: str, 程序名: str = None, 机器码: str = None, 备注: str = "官方版"):
        """
        根据卡密和卡密类型生成续卡支付链接
        
        参数:
            卡密: 要续费的卡密
            卡密类型: 续费的卡密类型，必须是有效的类型（时卡、天卡、周卡、月卡、季卡、年卡）
            程序名: 程序名称（可选）
            机器码: 机器码（可选）
            备注: 备注信息（可选）
        
        返回:
            dict: 包含成功状态、订单号、支付链接、金额和卡密类型的字典
        """
        try:
            # 验证卡密类型是否有效
            有效类型 = self.卡密类型配置['有效类型']
            if 卡密类型 not in 有效类型:
                return {"success": False, "message": f"无效的卡密类型，有效类型: {', '.join(有效类型)}"}
            
            # 从数据库获取金额
            金额 = self.获取卡片价格(卡密类型)
            if 金额 <= 0:
                return {"success": False, "message": f"卡密类型'{卡密类型}'没有设置价格"}
            
            # 生成订单号
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            random_num = str(random.randint(100, 999))
            订单号 = f"XK{timestamp}{random_num}"  # 续卡订单号添加XK前缀
            
            # 设置商品名称，添加续卡标识
            商品名称 = f"续卡-{卡密类型}"
            
            # 创建支付链接
            支付链接 = self.创建支付页面(订单号, 金额, 商品名称, 机器码, 程序名, 卡密类型, 备注, 卡密, True)
            
            if not 支付链接:
                return {"success": False, "message": "创建续卡支付链接失败"}
            
            return {
                "success": True,
                "order_no": 订单号,
                "payment_url": 支付链接,
                "amount": 金额,
                "card_type": 卡密类型,
                "card_key": 卡密,
                "remark": 备注
            }
        except Exception as e:
            logging.error(f"生成续卡支付链接时出错: {e}")
            return {"success": False, "message": f"生成续卡支付链接时出错: {str(e)}"}

# 添加支付相关路由处理函数
class 支付API处理:
    """支付API相关处理函数"""
    def __init__(self, 支付系统=None, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
        self.支付系统 = 支付系统 or 支付宝支付系统(数据库=self.数据库)
        self.卡密管理 = 卡密管理器(self.数据库)

    # 创建支付链接API端点
    def create_payment(self):
        """创建支付链接API端点"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'card_type' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密类型参数'
                }), 400

            # 获取参数
            卡密类型 = data.get('card_type')
            机器码 = data.get('machine_code')
            程序名 = data.get('app_name')
            备注 = data.get('remark', '官方版')
            
            # 生成支付链接
            result = self.支付系统.生成支付链接(程序名, 卡密类型, 机器码, 备注)
            
            return jsonify(result)
        except Exception as e:
            logging.error(f"创建支付链接API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 创建续卡支付链接API端点
    def create_renewal_payment(self):
        """创建续卡支付链接API端点"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'card_key' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密参数'
                }), 400
                
            if 'card_type' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密类型参数'
                }), 400

            # 获取参数
            卡密 = data.get('card_key')
            卡密类型 = data.get('card_type')
            机器码 = data.get('machine_code')
            程序名 = data.get('app_name')
            备注 = data.get('remark', '官方版')
            
            # 检查卡密是否存在
            卡密信息 = self.数据库.执行单条查询("SELECT * FROM 卡密系统 WHERE 卡密 = %s", (卡密,))
            if not 卡密信息:
                return jsonify({
                    'success': False,
                    'message': '卡密不存在，无法续卡'
                }), 400
            
            # 生成续卡支付链接
            result = self.支付系统.生成续卡支付链接(卡密, 卡密类型, 程序名, 机器码, 备注)
            
            return jsonify(result)
        except Exception as e:
            logging.error(f"创建续卡支付链接API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 提取代理秘钥处理函数
    def _处理代理统计(self, cursor, 代理秘钥, 当前时间, 当前时间字符串):
        """
        处理代理秘钥统计数据更新
        
        参数:
            cursor: 数据库游标
            代理秘钥: 代理秘钥
            当前时间: 当前时间对象
            当前时间字符串: 当前时间字符串
            
        返回:
            成功更新返回True，否则返回False
        """
        # 查询代理秘钥是否存在
        cursor.execute("SELECT * FROM 代理秘钥 WHERE 代理秘钥 = %s FOR UPDATE", (代理秘钥,))
        代理信息 = cursor.fetchone()
        
        if not 代理信息:
            return False
            
        # 获取当前日期和月份
        当前日期 = 当前时间.date()
        最近激活时间 = 代理信息.get('最近激活时间')
        
        # 检查是否需要重置今日激活数量
        今日激活数量 = 代理信息.get('今日激活数量', 0)
        if 最近激活时间 and 最近激活时间.date() < 当前日期:
            今日激活数量 = 0  # 如果是新的一天，重置今日激活数量
        
        # 检查是否需要重置本月激活数量
        本月激活数量 = 代理信息.get('本月激活数量', 0)
        if 最近激活时间 and 最近激活时间.month != 当前时间.month:
            本月激活数量 = 0  # 如果是新的一月，重置本月激活数量
        
        # 更新代理秘钥表
        cursor.execute(
            """
            UPDATE 代理秘钥 
            SET 最近激活时间 = %s, 
                今日激活数量 = %s,
                本月激活数量 = %s,
                激活总数 = 激活总数 + 1
            WHERE 代理秘钥 = %s
            """,
            (
                当前时间字符串,
                今日激活数量 + 1,
                本月激活数量 + 1,
                代理秘钥
            )
        )
        
        logging.info(f"已更新代理秘钥 {代理秘钥} 的激活统计信息")
        return True

    # 提取代理秘钥查询函数
    def _获取代理名字(self, cursor, 备注):
        """
        根据备注(代理秘钥)获取代理名字
        
        参数:
            cursor: 数据库游标
            备注: 备注信息，可能包含代理秘钥
            
        返回:
            (代理名字, 原始备注) 元组
        """
        if not 备注 or 备注 == "官方版":
            return 备注, 备注
            
        # 以备注为代理秘钥
        代理秘钥 = 备注
        
        # 查询代理秘钥对应的代理名字
        cursor.execute("SELECT 代理名字 FROM 代理秘钥 WHERE 代理秘钥 = %s", (代理秘钥,))
        代理信息 = cursor.fetchone()
        
        if 代理信息 and 代理信息.get('代理名字'):
            return 代理信息.get('代理名字'), 代理秘钥  # 返回代理名字和原始秘钥
        
        return 备注, 备注  # 没找到代理信息，返回原备注

    # 提取订单记录函数
    def _记录订单信息(self, cursor, 订单数据):
        """
        记录订单信息到数据库
        
        参数:
            cursor: 数据库游标
            订单数据: 包含订单信息的字典
        """
        cursor.execute(
            """
            INSERT INTO 支付订单 (订单号, 交易号, 金额, 机器码, 程序名, 卡密类型, 卡密, 支付时间, 有效期, 代理名字)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """,
            (
                订单数据['订单号'],
                订单数据['交易号'],
                订单数据['金额'],
                订单数据['机器码'] or "",
                订单数据['程序名'] or "",
                订单数据['卡密类型'],
                订单数据['卡密'],
                订单数据['支付时间'],
                订单数据['到期时间'],
                订单数据['代理名字']
            )
        )

    # 支付宝异步通知API端点
    def alipay_notify(self):
        """处理支付宝的支付结果通知"""
        try:
            # 获取通知数据
            数据 = request.form.to_dict()
            
            # 验证通知的真实性
            if not self.支付系统.验证支付宝通知(数据):
                return "fail"
            
            # 验证通知类型
            trade_status = 数据.get('trade_status')
            if trade_status != 'TRADE_SUCCESS':
                return "success"  # 返回success，避免支付宝重复通知
            
            # 获取订单信息
            订单号 = 数据.get('out_trade_no')
            交易号 = 数据.get('trade_no')
            金额 = float(数据.get('total_amount', 0))
            商品名称 = 数据.get('subject', '')
            
            # 获取回传参数
            回传参数 = 数据.get('passback_params', '')
            机器码 = None
            程序名 = None
            卡密类型 = None
            备注 = "官方版"  # 默认备注
            卡密 = None
            是否续卡 = False
            
            # 解析回传参数
            if 回传参数:
                import urllib.parse
                回传参数 = urllib.parse.unquote(回传参数)
                参数列表 = 回传参数.split('&')
                for 参数 in 参数列表:
                    if '=' in 参数:
                        键, 值 = 参数.split('=', 1)
                        if 键 == 'machine_code':
                            机器码 = 值
                        elif 键 == 'app_name':
                            程序名 = 值
                        elif 键 == 'card_type':
                            卡密类型 = 值
                        elif 键 == 'remark' or 键 == 'memo':
                            备注 = 值
                        elif 键 == 'card_key':
                            卡密 = 值
                        elif 键 == 'is_renewal' and 值 == '1':
                            是否续卡 = True
            
            # 如果回传参数中没有卡密类型，使用商品名称作为卡密类型
            if not 卡密类型:
                卡密类型 = 商品名称  # 直接使用商品名称作为卡密类型
                # 如果是续卡订单，商品名称可能是"续卡-XXX"格式，需要处理
                if 卡密类型.startswith("续卡-"):
                    卡密类型 = 卡密类型[3:]  # 移除"续卡-"前缀
            
            # 使用事务处理订单
            def 处理订单(conn, cursor):
                # 检查订单是否已经处理过（在事务中，使用行锁避免并发问题）
                cursor.execute("SELECT * FROM 支付订单 WHERE 订单号 = %s FOR UPDATE", (订单号,))
                已存在订单 = cursor.fetchone()
                
                if 已存在订单:
                    logging.info(f"订单 {订单号} 已经处理过，跳过")
                    return True
                
                当前时间 = datetime.datetime.now()
                当前时间字符串 = 当前时间.strftime('%Y-%m-%d %H:%M:%S')
                
                # 使用卡密管理器计算到期时间
                卡密管理器实例 = 卡密管理器()
                
                # 获取代理名字
                最终备注, 代理秘钥 = self._获取代理名字(cursor, 备注)
                
                # 如果是续卡，查询卡密信息并延长到期时间
                if 是否续卡 and 卡密:
                    # 查询卡密是否存在
                    cursor.execute("SELECT * FROM 卡密系统 WHERE 卡密 = %s FOR UPDATE", (卡密,))
                    卡密信息 = cursor.fetchone()
                    
                    if not 卡密信息:
                        logging.error(f"续卡失败：卡密 {卡密} 不存在")
                        return False
                    
                    # 获取原到期时间
                    原到期时间 = 卡密信息.get('到期时间')
                    
                    # 计算新的到期时间
                    新到期时间 = None
                    if 原到期时间:
                        # 如果原到期时间大于当前时间，在原基础上延长
                        if 原到期时间 > 当前时间:
                            _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 原到期时间)
                        else:
                            # 如果原到期时间已过，从当前时间开始计算
                            _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 当前时间)
                    else:
                        # 如果没有原到期时间，从当前时间开始计算
                        _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 当前时间)
                    
                    # 更新卡密到期时间
                    cursor.execute(
                        "UPDATE 卡密系统 SET 到期时间 = %s WHERE 卡密 = %s",
                        (到期时间字符串, 卡密)
                    )
                    
                    # 记录续卡订单信息
                    订单数据 = {
                        '订单号': 订单号,
                        '交易号': 交易号,
                        '金额': 金额,
                        '机器码': 机器码,
                        '程序名': 程序名,
                        '卡密类型': f"续卡-{卡密类型}",
                        '卡密': 卡密,
                        '支付时间': 当前时间字符串,
                        '到期时间': 到期时间字符串,
                        '代理名字': 最终备注
                    }
                    self._记录订单信息(cursor, 订单数据)
                    
                    # 更新代理秘钥表（如果备注中包含代理秘钥）- 续卡也要计数
                    if 代理秘钥 != 备注 or (备注 and 备注 != "官方版"):
                        self._处理代理统计(cursor, 代理秘钥 or 备注, 当前时间, 当前时间字符串)
                    
                    logging.info(f"续卡成功，订单号: {订单号}, 交易号: {交易号}, 金额: {金额}, 卡密: {卡密}")
                    return True
                
                # 非续卡订单，执行原有的创建新卡密逻辑
                _, 到期时间字符串 = 卡密管理器实例._计算到期时间(卡密类型, 当前时间)
                
                # 使用卡密管理器生成带前缀的随机卡密
                最终卡密 = None
                
                # 尝试最多10次生成唯一卡密
                for _ in range(10):
                    # 生成随机卡密
                    新卡密 = 卡密管理器实例._生成随机卡密(10)
                    
                    # 检查卡密是否已存在
                    cursor.execute("SELECT 卡密 FROM 卡密系统 WHERE 卡密 = %s", (新卡密,))
                    if cursor.fetchone():
                        continue  # 卡密已存在，重新生成
                    
                    最终卡密 = 新卡密
                    break
                
                if not 最终卡密:
                    logging.error(f"为订单 {订单号} 生成卡密失败，已尝试10次")
                    return False
                
                logging.info(f"为订单 {订单号} 生成新卡密: {最终卡密}")
                
                # 记录订单信息和生成的卡密
                订单数据 = {
                    '订单号': 订单号,
                    '交易号': 交易号,
                    '金额': 金额,
                    '机器码': 机器码,
                    '程序名': 程序名,
                    '卡密类型': 卡密类型,
                    '卡密': 最终卡密,
                    '支付时间': 当前时间字符串,
                    '到期时间': 到期时间字符串,
                    '代理名字': 最终备注
                }
                self._记录订单信息(cursor, 订单数据)
                
                # 将新卡密插入到卡密系统表
                cursor.execute(
                    """
                    INSERT INTO 卡密系统 (卡密, 类型, 机器码, 到期时间, 备注)
                    VALUES (%s, %s, %s, %s, %s)
                    """,
                    (最终卡密, 卡密类型, 机器码 or "", 到期时间字符串, 最终备注)
                )
                
                # 更新代理秘钥表（如果备注中包含代理秘钥）
                if 代理秘钥 != 备注 or (备注 and 备注 != "官方版"):
                    self._处理代理统计(cursor, 代理秘钥 or 备注, 当前时间, 当前时间字符串)
                
                logging.info(f"支付成功，订单号: {订单号}, 交易号: {交易号}, 金额: {金额}, 卡密: {最终卡密}")
                return True
            
            # 使用事务执行数据库操作
            self.数据库.在事务中执行(处理订单)
            
            return "success"  # 返回success告诉支付宝不要再次通知
        except Exception as e:
            logging.error(f"支付宝通知处理错误: {e}")
            return "fail"

    # 查询订单API端点
    def query_order(self):
        """查询订单API端点"""
        try:
            data = request.get_json()
            if not data or 'order_no' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少订单号参数'
                }), 400

            订单号 = data.get('order_no')

            # 直接从数据库查询订单信息
            订单信息 = self.数据库.执行单条查询(
                """
                SELECT 订单号, 交易号, 金额, 机器码, 程序名, 卡密类型, 卡密, 支付时间, 有效期
                FROM 支付订单 WHERE 订单号 = %s
                """,
                (订单号,)
            )

            if not 订单信息:
                return jsonify({
                    'success': False,
                    'message': '未找到订单信息或订单尚未支付'
                })

            return jsonify({
                'success': True,
                'message': '查询成功',
                'order_info': 订单信息
            })
        except Exception as e:
            logging.error(f"查询订单API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 查询价格API端点
    def query_prices(self):
        """查询卡密价格API端点"""
        try:
            # 从数据库获取价格配置
            价格配置 = self.数据库.执行单条查询(
                "SELECT 时卡, 天卡, 周卡, 月卡, 季卡, 年卡 FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            # 默认价格配置（当数据库查询失败时使用）
            默认价格配置 = self.支付系统.卡密类型配置['类型价格']

            # 构建价格响应数据
            prices = {}

            # 定义要返回的卡密类型
            卡密类型列表 = ['天卡', '周卡', '月卡', '季卡']

            # 字段名映射
            字段映射 = {
                '天卡': '天卡',
                '周卡': '周卡',
                '月卡': '月卡',
                '季卡': '季卡'
            }

            for 卡密类型 in 卡密类型列表:
                # 优先使用数据库中的价格
                if 价格配置 and 卡密类型 in 字段映射:
                    字段名 = 字段映射[卡密类型]
                    if 字段名 in 价格配置 and 价格配置[字段名] is not None:
                        prices[卡密类型] = float(价格配置[字段名])
                    else:
                        # 如果数据库中没有，使用默认价格
                        prices[卡密类型] = float(默认价格配置.get(卡密类型, 0.0))
                else:
                    # 如果数据库查询失败，使用默认价格
                    prices[卡密类型] = float(默认价格配置.get(卡密类型, 0.0))

            return jsonify({
                'success': True,
                'message': '价格查询成功',
                'data': {
                    'prices': prices,
                    'currency': 'CNY'
                }
            })
        except Exception as e:
            logging.error(f"查询价格API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 支付结果页面
    def payment_result(self):
        """处理支付宝同步回调，显示支付结果页面"""
        try:
            # 获取订单号
            订单号 = request.args.get('out_trade_no')
            if not 订单号:
                return self._生成支付结果页面("查询失败", "缺少订单号参数", None)
            
            # 从数据库查询订单和卡密信息
            订单信息 = self.数据库.执行单条查询(
                """
                SELECT 订单号, 交易号, 金额, 机器码, 程序名, 卡密类型, 卡密, 支付时间, 有效期
                FROM 支付订单 WHERE 订单号 = %s
                """, 
                (订单号,)
            )
            
            if not 订单信息:
                return self._生成支付结果页面(
                    "订单查询",
                    "未找到订单信息，订单可能尚未支付成功或正在处理中", 
                    {"订单号": 订单号, "状态": "处理中"}
                )
            
            return self._生成支付结果页面(
                "支付成功", 
                "恭喜您，支付成功！以下是您的卡密信息", 
                订单信息
            )
            
        except Exception as e:
            logging.error(f"支付结果页面生成错误: {e}")
            return self._生成支付结果页面("系统错误", f"系统错误: {str(e)}", None)
    
    def _生成支付结果页面(self, 标题, 消息, 订单信息):
        """生成支付结果HTML页面"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{标题} - 卡密验证系统</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                    color: #333;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background-color: #fff;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
                }}
                h1 {{
                    color: #0066cc;
                    margin-bottom: 20px;
                    text-align: center;
                }}
                .message {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 15px;
                    background-color: #f0f7ff;
                    border-radius: 4px;
                }}
                .card-info {{
                    border: 1px solid #ddd;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .label {{
                    font-weight: bold;
                    color: #666;
                    min-width: 120px;
                    display: inline-block;
                }}
                .item {{
                    margin-bottom: 10px;
                }}
                .important {{
                    color: #e74c3c;
                    font-weight: bold;
                }}
                .refresh {{
                    text-align: center;
                    margin-top: 20px;
                    color: #0066cc;
                    cursor: pointer;
                }}
                .download-btn {{
                    display: block;
                    text-align: center;
                    margin: 20px auto;
                    padding: 10px 20px;
                    background-color: #4CAF50;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                    font-weight: bold;
                }}
                .card-list {{
                    border: 1px solid #eee;
                    padding: 10px;
                    margin-top: 15px;
                    border-radius: 4px;
                    background-color: #f9f9f9;
                    max-height: 200px;
                    overflow-y: auto;
                }}
                .card-item {{
                    margin-bottom: 8px;
                    padding-bottom: 8px;
                    border-bottom: 1px dashed #ddd;
                }}
                .card-item:last-child {{
                    border-bottom: none;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>{标题}</h1>
                <div class="message">{消息}</div>
        """
        
        # 如果有订单信息，显示详细信息
        if 订单信息:
            html += '<div class="card-info">'
            
            # 检查是否是批量订单（通过订单号前缀PL判断）
            是批量订单 = False
            if "订单号" in 订单信息 and isinstance(订单信息.get("订单号"), str) and 订单信息.get("订单号").startswith("PL"):
                是批量订单 = True
            
            # 显示基本订单信息
            if "订单号" in 订单信息:
                html += f'<div class="item"><span class="label">订单号：</span>{订单信息.get("订单号", "N/A")}</div>'
            
            if "金额" in 订单信息:
                html += f'<div class="item"><span class="label">支付金额：</span>￥{订单信息.get("金额", "N/A")}</div>'
            
            if "卡密类型" in 订单信息:
                html += f'<div class="item"><span class="label">卡密类型：</span>{订单信息.get("卡密类型", "N/A")}</div>'
            
            # if "支付时间" in 订单信息:
            #     html += f'<div class="item"><span class="label">支付时间：</span>{订单信息.get("支付时间", "N/A")}</div>'
            
            if "有效期" in 订单信息:
                html += f'<div class="item"><span class="label">有效期至：</span>{订单信息.get("有效期", "N/A")}</div>'
            
            # 对于批量订单，提供下载卡密文件的链接而不是直接显示卡密
            if 是批量订单:
                html += '<p class="important">您购买的是批量卡密，请点击下方按钮下载所有卡密。</p>'
                
                # 修改下载链接，指向正确的批量购卡系统地址
                批量购卡URL = "http://***************:5104"  # 批量购卡系统的公网地址和端口
                下载链接 = f"{批量购卡URL}/api/agent/buy/download?order_no={订单信息.get('订单号', '')}&card_type={订单信息.get('卡密类型', '')}&quantity=10"
                html += f'<a href="{下载链接}" class="download-btn">下载卡密文件</a>'
                
                # 尝试从订单号中查询出所有卡密，直接显示在页面上
                import mysql.connector
                from 支付系统 import 数据库管理器
                
                try:
                    数据库 = 数据库管理器()
                    卡密列表 = 数据库.执行查询(
                        """
                        SELECT 卡密, 类型, 到期时间 FROM 卡密系统
                        WHERE 备注 LIKE %s
                        """,
                        [f"%{订单信息.get('订单号', '')}%"]
                    )
                    
                    if 卡密列表 and len(卡密列表) > 0:
                        html += '<div class="item"><span class="label">卡密列表：</span></div>'
                        html += '<div class="card-list">'
                        for i, 卡密 in enumerate(卡密列表, 1):
                            # 直接使用下标访问，避免使用.get方法
                            卡密字符串 = 卡密['卡密'] if '卡密' in 卡密 else ""
                            类型 = 卡密['类型'] if '类型' in 卡密 else ""
                            到期时间 = 卡密['到期时间'] if '到期时间' in 卡密 else ""
                            
                            # 处理None或空的到期时间，显示为"未激活"
                            if 到期时间 is None or 到期时间 == "None" or not 到期时间:
                                到期时间 = "未激活"
                                
                            html += f'<div class="card-item">{i}. <span class="important">{卡密字符串}</span> - {类型} - 到期时间: {到期时间}</div>'
                        html += '</div>'
                except Exception as e:
                    import logging
                    logging.error(f"在支付结果页面查询卡密列表失败: {e}")
            else:
                # 非批量订单，显示单个卡密
                if "卡密" in 订单信息:
                    html += f'<div class="item"><span class="label">卡密：</span><span class="important">{订单信息.get("卡密", "N/A")}</span></div>'
                    html += '<p class="important">请妥善保管您的卡密，切勿泄露给他人！</p>'
            
            if "状态" in 订单信息:
                html += f'<div class="item"><span class="label">订单状态：</span>{订单信息.get("状态", "N/A")}</div>'
                
                # 如果订单状态为处理中，添加自动刷新
                if 订单信息.get("状态") == "处理中":
                    html += """
                    <div class="refresh" onclick="location.reload()">刷新页面查看最新状态</div>
                    <script>
                        // 30秒后自动刷新页面
                        setTimeout(function() { location.reload(); }, 30000);
                    </script>
                    """
            
            html += '</div>'
            
        html += """
            </div>
        </body>
        </html>
        """
        return html

def 注册支付路由(app, 支付系统实例=None, 数据库实例=None):
    """
    注册支付相关的路由到Flask应用
    
    参数:
        app: Flask应用实例
        支付系统实例: 支付宝支付系统实例（可选）
        数据库实例: 数据库管理器实例（可选）
    """
    支付处理 = 支付API处理(支付系统实例, 数据库实例)
    
    # 注册核心路由
    app.route('/create_payment', methods=['POST'])(支付处理.create_payment) # 创建支付链接
    app.route('/create_renewal_payment', methods=['POST'])(支付处理.create_renewal_payment) # 创建续卡支付链接
    app.route('/alipay_notify', methods=['POST'])(支付处理.alipay_notify) # 支付宝异步通知
    app.route('/query_order', methods=['POST'])(支付处理.query_order) # 查询订单
    app.route('/query_prices', methods=['GET'])(支付处理.query_prices) # 查询价格
    app.route('/payment_result', methods=['GET'])(支付处理.payment_result) # 支付结果
    
    return 支付处理

# 添加主程序入口，支持独立运行
if __name__ == '__main__':
    from flask import Flask
    
    # 创建Flask应用
    app = Flask(__name__)
    
    # 创建支付系统和数据库实例
    支付系统实例 = 支付宝支付系统()
    数据库实例 = 数据库管理器()
    
    # 更新支付宝配置中的回调URL
    Config.支付宝配置['return_url'] = f"{Config.get_public_url()}/payment_result"
    Config.支付宝配置['notify_url'] = f"{Config.get_public_url()}/alipay_notify"
    
    # 注册支付相关路由
    注册支付路由(app, 支付系统实例, 数据库实例)
    
    # 获取服务配置
    服务配置 = Config.服务配置
    
    # 输出简化的系统信息
    print(f"卡密支付系统启动 - 访问地址: http://{服务配置['host']}:{服务配置['port']}")
    print(f"管理员卡密生成页面: http://{服务配置['host']}:{服务配置['port']}/admin/cards")
    
    # 启动Flask应用
    app.run(
        host=服务配置['host'],
        port=服务配置['port'],
        debug=服务配置['debug']
    )