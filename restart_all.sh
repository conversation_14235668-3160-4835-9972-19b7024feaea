#!/bin/bash
echo "重启所有服务..."

# 先停止所有服务
echo "正在停止服务..."
pkill -f "支付系统.py"
pkill -f "免费限制.py" 
pkill -f "付费限制.py"
pkill -f "邮箱监控.py"

# 等待进程完全停止
sleep 3

# 切换到工作目录
cd /opt

echo "正在启动服务..."

# 启动支付系统 (端口5001)
nohup python3 支付系统.py > 支付系统.log 2>&1 &
echo "支付系统已启动 - 端口5001"

# 启动免费限制服务 (端口5269)
nohup python3 免费限制.py > 免费限制.log 2>&1 &
echo "免费限制服务已启动 - 端口5269"

# 启动付费限制服务 (端口5270)
nohup python3 付费限制.py > 付费限制.log 2>&1 &
echo "付费限制服务已启动 - 端口5270"

# 启动邮箱监控
nohup python3 邮箱监控.py > 邮箱监控.log 2>&1 &
echo "邮箱监控已启动"

echo "所有服务重启完成！"
