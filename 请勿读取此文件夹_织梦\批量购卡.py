import os
import logging
import random
import datetime
from flask import Flask, request, jsonify, Response, redirect, make_response
from 支付系统 import Config, 数据库管理器, 支付宝支付系统, 卡密管理器

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# 定义自定义端口配置
购卡系统配置 = {
    'port': 5104  # 使用不同于支付系统的端口
}

class 批量购卡系统:
    """批量购卡系统类"""
    def __init__(self):
        # 卡密类型配置
        self.卡密类型配置 = {
            '有效类型': ['天卡', '周卡', '月卡', '季卡', '年卡'],
            '类型价格': {
                '天卡': 6.6,
                '周卡': 26.9,
                '月卡': 49.9,
                '季卡': 89.9,
                '年卡': 360.0
            }
        }
        self.支付系统 = 支付宝支付系统()
        self.数据库 = 数据库管理器()
        self.卡密管理 = 卡密管理器(self.数据库)
    
    def 计算折扣(self, 数量):
        """计算批量购买折扣"""
        if 数量 < 5:
            return 1.0
        elif 数量 <= 9:
            return 0.8
        elif 数量 <= 49:
            return 0.7
        elif 数量 <= 99:
            return 0.6
        else:
            return 0.5
    
    def 获取卡密类型列表(self):
        """获取卡密类型列表"""
        有效类型 = self.卡密类型配置['有效类型']
        类型价格 = self.卡密类型配置['类型价格']
        卡密类型列表 = []
        for 类型 in 有效类型:
            卡密类型列表.append({
                'type': 类型,
                'price': 类型价格.get(类型, 0)
            })
        return 卡密类型列表
    
    def 查询购卡页面(self):
        """返回批量购卡HTML页面"""
        try:
            html_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "批量购卡.html")
            with open(html_file_path, "r", encoding="utf-8") as f:
                html_content = f.read()
            return Response(html_content, mimetype='text/html')
        except Exception as e:
            logging.error(f"读取批量购卡HTML文件失败: {e}")
            return f"<h1>错误</h1><p>无法加载批量购卡页面: {str(e)}</p>"
            
    def 创建批量订单(self, 卡密类型, 数量):
        """创建批量购卡订单"""
        try:
            # 验证卡密类型
            if 卡密类型 not in self.卡密类型配置['有效类型']:
                return False, f"无效的卡密类型: {卡密类型}"
                
            # 验证数量
            try:
                数量 = int(数量)
                if 数量 < 1:
                    return False, "购买数量必须大于等于1"
                if 数量 > 100:
                    return False, "单次最多购买100张卡密"
            except:
                return False, "无效的数量参数"
                
            # 获取卡密单价
            单价 = self.卡密类型配置['类型价格'].get(卡密类型, 0)
            
            # 计算折扣
            折扣 = self.计算折扣(数量)
            折后单价 = 单价 * 折扣
            总价 = 折后单价 * 数量
            
            # 生成批量订单参数
            订单参数 = {
                "card_type": 卡密类型,
                "quantity": 数量,
                "discount": 折扣,
                "unit_price": 单价,
                "discount_price": 折后单价,
                "total_price": 总价,
                "备注": f"批量购买{卡密类型}x{数量}"
            }
            
            # 生成支付链接
            商品名称 = f"批量{卡密类型}x{数量}"
            
            # 创建一个定制版的创建支付页面请求
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            random_num = str(random.randint(100, 999))
            订单号 = f"PL{timestamp}{random_num}"  # 批量订单前缀
            
            # 在支付链接中明确添加数量信息
            支付链接 = self.支付系统.创建支付页面(
                订单号=订单号,
                总金额=总价,
                商品名称=商品名称,
                机器码=f"批量{数量}",  # 在机器码中添加数量信息
                程序名=f"批量购卡_{数量}张",  # 在程序名中添加数量信息
                卡密类型=卡密类型,
                备注=f"批量{数量}张_{卡密类型}"  # 确保备注中包含数量信息
            )
            
            if not 支付链接:
                return False, "创建支付链接失败"
                
            # 保存批量订单信息到数据库，标记为未支付
            try:
                self.数据库.执行更新(
                    """
                    INSERT INTO 批量订单 (订单号, 卡密类型, 数量, 单价, 折扣, 折后单价, 总价, 创建时间, 支付状态, 备注)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (
                        订单号, 
                        卡密类型, 
                        数量, 
                        单价, 
                        折扣, 
                        折后单价, 
                        总价, 
                        datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        0,  # 0表示未支付
                        f"批量购买{卡密类型}x{数量}"
                    )
                )
            except Exception as e:
                logging.error(f"保存批量订单信息失败: {e}")
                # 即使保存失败也继续，这不影响支付流程
                
            # 添加日志以便调试
            logging.info(f"创建批量订单成功: 类型={卡密类型}, 数量={数量}, 订单号={订单号}")
                
            # 返回订单信息和支付链接
            return True, {
                "order_info": 订单参数,
                "支付链接": 支付链接,
                "订单号": 订单号
            }
            
        except Exception as e:
            logging.error(f"创建批量订单失败: {e}")
            return False, f"创建订单失败: {str(e)}"
    
    def 批量生成卡密(self, 订单号, 卡密类型, 数量):
        """批量生成指定数量的卡密"""
        try:
            生成的卡密列表 = []
            当前时间 = datetime.datetime.now()
            
            # 使用事务生成卡密
            def 生成卡密事务(conn, cursor):
                生成结果 = []
                成功生成数量 = 0
                
                for _ in range(数量):
                    if 成功生成数量 >= 数量:  # 确保不会生成超过请求数量
                        break

                    # 生成随机卡密
                    新卡密 = self.卡密管理._生成随机卡密(10)
                    
                    # 检查卡密是否已存在
                    cursor.execute("SELECT 卡密 FROM 卡密系统 WHERE 卡密 = %s", (新卡密,))
                    if cursor.fetchone():
                        continue  # 卡密已存在，重新生成
                    
                    # 批量购买的卡密初始不设置到期时间，只有激活后才开始计时
                    cursor.execute(
                        """
                        INSERT INTO 卡密系统 (卡密, 类型, 机器码, 备注)
                        VALUES (%s, %s, %s, %s)
                        """,
                        (新卡密, 卡密类型, "", f"批量订单:{订单号}")
                    )
                    
                    # 添加到结果列表
                    生成结果.append({
                        "卡密": 新卡密,
                        "类型": 卡密类型,
                        "到期时间": "未激活"
                    })
                    成功生成数量 += 1
                
                # 更新批量订单状态为已支付
                cursor.execute(
                    """
                    UPDATE 批量订单 SET 支付状态 = 1, 支付时间 = %s, 卡密数量 = %s
                    WHERE 订单号 = %s
                    """,
                    (当前时间.strftime('%Y-%m-%d %H:%M:%S'), 成功生成数量, 订单号)
                )
                
                return 生成结果
            
            # 执行事务
            生成的卡密列表 = self.数据库.在事务中执行(生成卡密事务)
            
            if not 生成的卡密列表:
                return False, "生成卡密失败，请重试"
            
            logging.info(f"批量订单 {订单号} 已成功生成 {len(生成的卡密列表)} 个卡密")
            return True, 生成的卡密列表
            
        except Exception as e:
            logging.error(f"批量生成卡密失败: {e}")
            return False, f"生成失败: {str(e)}"

    def 查询订单支付状态(self, 订单号):
        """查询订单支付状态并处理支付成功的订单"""
        try:
            # 检查订单是否已经处理
            批量订单 = self.数据库.执行单条查询(
                "SELECT * FROM 批量订单 WHERE 订单号 = %s", 
                [订单号]
            )
            
            if not 批量订单:
                return False, "订单不存在"
            
            # 注意：这里忽略linter错误，mysql-connector返回的是字典，可以使用字符串键访问
            # 检查订单是否已支付
            支付状态 = 0
            if isinstance(批量订单, dict) and '支付状态' in 批量订单:
                支付状态 = 批量订单['支付状态']  # linter错误但代码正常工作
            
            if 支付状态 == 1:
                return True, "订单已支付"
            
            # 从支付订单表查询支付状态
            支付订单 = self.数据库.执行单条查询(
                "SELECT * FROM 支付订单 WHERE 订单号 = %s", 
                [订单号]
            )
            
            if not 支付订单:
                return False, "订单未支付"
            
            # 订单已支付，处理批量生成卡密
            卡密类型 = None
            数量 = None
            
            # 注意：这里忽略linter错误，mysql-connector返回的是字典，可以使用字符串键访问
            if isinstance(批量订单, dict):
                if '卡密类型' in 批量订单:
                    卡密类型 = 批量订单['卡密类型']  # linter错误但代码正常工作
                if '数量' in 批量订单:
                    数量 = 批量订单['数量']  # linter错误但代码正常工作
            
            if not 卡密类型 or not 数量:
                return False, "订单数据不完整"
            
            # 批量生成卡密
            成功, 结果 = self.批量生成卡密(订单号, 卡密类型, 数量)
            
            if 成功:
                return True, {
                    "message": f"订单已支付并生成 {len(结果)} 个卡密",
                    "card_list": 结果
                }
            else:
                return False, f"处理订单失败: {结果}"
            
        except Exception as e:
            logging.error(f"查询订单支付状态失败: {e}")
            return False, f"查询失败: {str(e)}"

class 批量购卡API:
    """批量购卡API处理类"""
    def __init__(self, 购卡系统=None):
        self.购卡系统 = 购卡系统 or 批量购卡系统()
    
    def _接口响应(self, 成功状态, 消息, 数据=None):
        """统一的API响应格式"""
        response = jsonify({
            'success': 成功状态,
            'message': 消息,
            **(数据 if isinstance(数据, dict) else {'data': 数据})
        })
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        return response
    
    def _生成卡密文本(self, 卡密列表, 卡密类型, 订单号):
        """生成包含卡密的文本文件"""
        当前时间 = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        文本内容 = f"批量{卡密类型}卡密列表 - 订单号: {订单号} - 导出时间: {当前时间}\n\n"
        
        for i, 卡密 in enumerate(卡密列表, 1):
            到期时间 = 卡密.get('到期时间', '') if isinstance(卡密, dict) else getattr(卡密, '到期时间', '')
            # 如果到期时间是None或空字符串，显示为"未激活"
            if not 到期时间 or 到期时间 == "None":
                到期时间 = "未激活"
            文本内容 += f"{i}. {卡密.get('卡密', '') if isinstance(卡密, dict) else getattr(卡密, '卡密', '')} - {卡密类型} - 到期时间: {到期时间}\n"
        
        文本内容 += f"\n共 {len(卡密列表)} 个卡密\n"
        文本内容 += "注意：卡密未激活前不计时，请妥善保管"
        
        return 文本内容
        
    def 下载卡密文件(self):
        """提供卡密下载接口"""
        try:
            订单号 = request.args.get('order_no')
            if not 订单号:
                return "缺少订单号参数", 400
                
            卡密类型 = request.args.get('card_type', '未知')
            数量 = int(request.args.get('quantity', 0))
            
            # 查询订单支付状态并获取卡密
            支付成功, 结果 = self.购卡系统.查询订单支付状态(订单号)
            
            if not 支付成功:
                # 如果订单未支付或处理失败，返回错误信息
                return f"订单处理失败: {结果}", 400
            
            卡密列表 = []
            
            # 如果返回的是字典并包含卡密列表
            if isinstance(结果, dict) and '卡密列表' in 结果:
                卡密列表 = 结果['卡密列表']
            elif isinstance(结果, dict) and 'card_list' in 结果:
                卡密列表 = 结果['card_list']
            
            # 如果没有找到卡密，从数据库查询
            if not 卡密列表:
                # 查询数据库中与该订单关联的卡密 - 去掉ORDER BY ID
                卡密列表 = self.购卡系统.数据库.执行查询(
                    """
                    SELECT 卡密, 类型, 到期时间 FROM 卡密系统
                    WHERE 备注 LIKE %s
                    """,
                    [f"%{订单号}%"]
                )
            
            # 如果仍然没有卡密，使用模拟数据（仅用于测试）
            if not 卡密列表:
                logging.warning(f"订单 {订单号} 没有找到关联的卡密数据，使用模拟数据")
                # 模拟生成指定数量的卡密
                for i in range(1, 数量 + 1):
                    卡密列表.append({
                        "卡密": f"TEST{订单号[:6]}{i:03d}",
                        "类型": 卡密类型,
                        "到期时间": "激活后计算"
                    })
            
            文本内容 = self._生成卡密文本(卡密列表, 卡密类型, 订单号)
            
            # 创建文本响应
            response = make_response(文本内容)
            response.headers.set('Content-Type', 'text/plain; charset=utf-8')
            
            # 使用ASCII字符作为文件名，避免编码问题
            安全文件名 = f"card_keys_{订单号[:8]}.txt"
            response.headers.set('Content-Disposition', f'attachment; filename="{安全文件名}"')
            
            return response
        except Exception as e:
            logging.error(f"下载卡密文件失败: {e}")
            return "下载失败，请联系客服", 500

    def 查询卡密信息(self):
        """查询卡密类型和价格信息API"""
        try:
            if request.method == 'OPTIONS':
                return self._接口响应(True, "跨域预检成功")
            
            卡密类型列表 = self.购卡系统.获取卡密类型列表()
            return self._接口响应(True, "查询成功", {
                'card_types': 卡密类型列表
            })
        except Exception as e:
            logging.error(f"查询卡密信息API错误: {e}")
            return self._接口响应(False, f"服务器错误: {str(e)}")

    def 创建订单(self):
        """创建批量购卡订单API"""
        try:
            if request.method == 'OPTIONS':
                return self._接口响应(True, "跨域预检成功")
                
            # 获取请求数据
            data = request.get_json()
            if not data:
                return self._接口响应(False, "缺少请求数据")
                
            卡密类型 = data.get('card_type')
            数量 = data.get('quantity', 1)
            
            if not 卡密类型:
                return self._接口响应(False, "缺少卡密类型参数")
                
            # 创建订单
            成功, 结果 = self.购卡系统.创建批量订单(卡密类型, 数量)
            
            if 成功:
                # 创建订单成功，添加下载链接
                if isinstance(结果, dict):
                    订单号 = 结果.get('订单号', '')
                    下载链接 = f"/api/agent/buy/download?order_no={订单号}&card_type={卡密类型}&quantity={数量}"
                    结果['下载链接'] = 下载链接
                return self._接口响应(True, "创建订单成功", 结果)
            else:
                return self._接口响应(False, 结果)
                
        except Exception as e:
            logging.error(f"创建订单API错误: {e}")
            return self._接口响应(False, f"服务器错误: {str(e)}")
            
    def 查询订单状态(self):
        """查询订单状态API"""
        try:
            if request.method == 'OPTIONS':
                return self._接口响应(True, "跨域预检成功")
                
            # 获取请求数据
            订单号 = request.args.get('order_no')
            if not 订单号:
                return self._接口响应(False, "缺少订单号参数")
                
            # 查询订单状态
            成功, 结果 = self.购卡系统.查询订单支付状态(订单号)
            
            if 成功:
                return self._接口响应(True, "查询成功", {
                    "paid": True,
                    "message": "订单已支付",
                    "data": 结果 if isinstance(结果, dict) else {"message": 结果}
                })
            else:
                return self._接口响应(True, "查询成功", {
                    "paid": False,
                    "message": 结果
                })
                
        except Exception as e:
            logging.error(f"查询订单状态API错误: {e}")
            return self._接口响应(False, f"服务器错误: {str(e)}")

def 获取批量购卡系统路由配置():
    """
    返回批量购卡系统路由配置，用于在其他文件中注册路由
    
    返回:
        dict: 包含路由信息的字典
    """
    购卡系统 = 批量购卡系统()
    购卡API = 批量购卡API(购卡系统)
    
    return {
        'api路由': [
            {
                'url': '/api/agent/buy/info',
                'view_func': 购卡API.查询卡密信息,
                'methods': ['POST', 'GET', 'OPTIONS']
            },
            {
                'url': '/api/agent/buy/create_order',
                'view_func': 购卡API.创建订单,
                'methods': ['POST', 'OPTIONS']
            },
            {
                'url': '/api/agent/buy/check_order',
                'view_func': 购卡API.查询订单状态,
                'methods': ['GET', 'OPTIONS']
            },
            {
                'url': '/api/agent/buy/download',
                'view_func': 购卡API.下载卡密文件,
                'methods': ['GET']
            }
        ],
        '页面路由': [
            {
                'url': '/agent/buy/',
                'view_func': 购卡系统.查询购卡页面,
                'methods': ['GET']
            }
        ],
        '系统实例': {
            '购卡系统': 购卡系统,
            '购卡API': 购卡API
        }
    }

# 如果作为独立文件运行，可以进行测试
if __name__ == '__main__':
    from flask import Flask
    
    app = Flask(__name__)
    
    # 检查数据库表是否存在，如果不存在则创建
    try:
        数据库 = 数据库管理器()
        conn = 数据库.获取连接()
        cursor = conn.cursor()
        
        # 检查批量订单表是否存在
        cursor.execute("""
            SHOW TABLES LIKE '批量订单'
        """)
        
        if not cursor.fetchone():
            # 创建批量订单表
            cursor.execute("""
                CREATE TABLE `批量订单` (
                    `ID` int NOT NULL AUTO_INCREMENT,
                    `订单号` varchar(32) NOT NULL,
                    `卡密类型` varchar(10) NOT NULL,
                    `数量` int NOT NULL,
                    `单价` float NOT NULL,
                    `折扣` float NOT NULL,
                    `折后单价` float NOT NULL,
                    `总价` float NOT NULL,
                    `创建时间` datetime NOT NULL,
                    `支付时间` datetime DEFAULT NULL,
                    `支付状态` tinyint NOT NULL DEFAULT '0',
                    `卡密数量` int DEFAULT '0',
                    `备注` varchar(255) DEFAULT NULL,
                    PRIMARY KEY (`ID`),
                    UNIQUE KEY `订单号` (`订单号`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            conn.commit()
            logging.info("成功创建批量订单表")
        
        cursor.close()
        conn.close()
    except Exception as e:
        logging.error(f"检查数据库表结构时出错: {e}")
    
    # 获取路由配置
    路由配置 = 获取批量购卡系统路由配置()
    
    # 手动注册路由
    for route in 路由配置['api路由']:
        app.add_url_rule(
            route['url'], 
            view_func=route['view_func'], 
            methods=route['methods']
        )
        
    for route in 路由配置['页面路由']:
        app.add_url_rule(
            route['url'], 
            view_func=route['view_func'], 
            methods=route['methods']
        )
    
    print(f"批量购卡系统运行于: http://127.0.0.1:{购卡系统配置['port']}/agent/buy/")
    
    # 运行开发服务器
    app.run(
        host=Config.服务配置['host'], 
        port=购卡系统配置['port'],
        debug=Config.服务配置['debug']
    )
