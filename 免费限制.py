#此脚本在服务端运行，用于检查免费限制 - 独立版本
import datetime
import logging
import time
import threading
import os
import json
import random
import string
from flask import Flask, request, jsonify
import requests
import hashlib
import base64
import struct
import mysql.connector
from mysql.connector import pooling

# 配置日志
from logging.handlers import RotatingFileHandler

# 创建日志格式
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(threadName)s] - %(filename)s:%(lineno)d - %(message)s')

# 创建自定义过滤器，只允许特定关键日志通过
class 关键日志过滤器(logging.Filter):
    def filter(self, record):
        # 警告、错误和严重错误级别始终通过
        if record.levelno >= logging.WARNING:
            return True

        # 特定的关键信息性日志也允许通过
        if record.levelno == logging.INFO:
            关键信息 = [
                "成功获取", "验证成功", "验证失败", "服务启动",
                "已删除", "调用频率限制", "号池已关闭", "版本过低",
                "卡密不存在", "卡密已过期", "卡密已被禁用", "账号授权检查"
            ]

            # 如果日志消息包含任何关键词，允许通过
            for 关键词 in 关键信息:
                if 关键词 in record.getMessage():
                    return True

            # 其他INFO级别日志不通过
            return False

        # 其他级别（如DEBUG）默认不通过
        return False

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(log_formatter)

# 创建文件处理器（带轮转功能）
# maxBytes=10485760 表示日志文件达到10MB时轮转
# backupCount=5 表示保留5个备份文件
file_handler = RotatingFileHandler(
    filename='免费限制.log',
    mode='w',  # 修改为'w'模式，每次启动时覆盖之前的日志
    maxBytes=10485760,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
file_handler.setFormatter(log_formatter)

# 配置根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)  # 保持INFO级别，但通过过滤器控制
root_logger.addHandler(console_handler)
root_logger.addHandler(file_handler)

# 添加自定义过滤器
root_logger.addFilter(关键日志过滤器())

# 免费账号特定配置
免费账号模块配置 = {
    # 基础限制配置
    'IP每日最大设备数': 2,  # 同一IP每天最多允许1个不同设备获取账号，0表示不限制
    '设备每日最大获取账号数': 1,  # 每个设备每天最多获取1次账号，0表示不限制
    'IP每日最大获取次数': 2,  # 每个IP每天最多获取账号次数

    # 安全相关配置
    '同秒访问检测阈值': 3,  # 同一秒内访问多少次后自动禁用IP（访问时间记录保留数量等于此值）
    '设备锁定超时秒数': 2,  # 防止并发操作的锁定时间
    '启用IP限制检查': True,  # 是否启用IP限制功能
    '启用设备限制检查': True,  # 是否启用设备限制功能
    '启用IP天数循环限制': True,  # 是否启用IP基于星期几的循环使用限制（第一次可用，后续所有天都不能获取）

    # 缓存相关配置
    '设备信息缓存秒数': 300,  # 用于缓存从数据库获取的设备信息，单位秒（5分钟）
    '系统配置缓存秒数': 60,  # 系统配置缓存时间
    'IP信息缓存秒数': 300,  # IP信息缓存时间
    '缓存清理间隔秒数': 300,  # 缓存清理任务执行间隔

    # 错误处理配置
    'API超时秒数': 10,  # 调用外部API的超时时间
    '数据库连接超时秒数': 10,  # 数据库连接超时时间
    '最大重试次数': 3,  # 失败时的最大重试次数

    # 日志相关配置
    '启用详细日志': True,  # 是否启用详细日志记录
    '日志文件最大大小MB': 10,  # 日志文件最大大小
    '日志备份文件数量': 5,  # 保留的日志备份文件数量

    # 业务逻辑配置
    '邮箱后缀': "gmail.com",  # 返回给客户端的虚假邮箱后缀
    '删除原因_正常使用': "正常使用"  # 正常使用时的删除原因
}

# 生成虚假邮箱的函数
def 生成虚假邮箱():
    """生成一个带有随机前缀的虚假邮箱地址"""
    随机前缀 = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
    return f"{随机前缀}@{免费账号模块配置['邮箱后缀']}"

# 重新配置日志处理器以使用配置项
def 重新配置日志处理器():
    """在配置定义后重新配置日志处理器"""
    global file_handler, root_logger

    # 移除旧的文件处理器
    root_logger.removeHandler(file_handler)

    # 创建新的文件处理器，使用配置项
    new_file_handler = RotatingFileHandler(
        filename='免费限制.log',
        mode='w',
        maxBytes=免费账号模块配置['日志文件最大大小MB'] * 1024 * 1024,  # 转换为字节
        backupCount=免费账号模块配置['日志备份文件数量'],
        encoding='utf-8'
    )
    new_file_handler.setFormatter(log_formatter)

    # 添加新的文件处理器
    root_logger.addHandler(new_file_handler)

    # 更新全局变量
    file_handler = new_file_handler

    logging.info(f"日志配置已更新 - 最大文件大小: {免费账号模块配置['日志文件最大大小MB']}MB, 备份数量: {免费账号模块配置['日志备份文件数量']}")

# 应用新的日志配置
重新配置日志处理器()

# 数据库配置
数据库配置 = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'Yuyu6709.',  # 请替换为您的实际密码
    'database': 'kami3162',
    'pool_name': 'mysql_free_pool',
    'pool_size': 32,
    'pool_reset_session': True,
    'autocommit': True,
    'connection_timeout': 10,
    'use_pure': True,
    'get_warnings': True,
}

# 缓存管理器类
class 缓存管理器:
    def __init__(self, 默认过期时间=300):
        self.缓存 = {}
        self.过期时间 = {}
        self.默认过期时间 = 默认过期时间
        self.锁 = threading.RLock()
        self.启动清理线程()
        logging.info(f"缓存管理器初始化，默认过期时间: {默认过期时间}秒")

    def get(self, key, default=None):
        with self.锁:
            if key in self.缓存 and (key not in self.过期时间 or self.过期时间[key] > time.time()):
                return self.缓存[key]
            return default

    def set(self, key, value, ttl=None):
        with self.锁:
            self.缓存[key] = value
            if ttl is None:
                ttl = self.默认过期时间
            if ttl > 0:
                self.过期时间[key] = time.time() + ttl
            return True

    def delete(self, key):
        with self.锁:
            if key in self.缓存:
                del self.缓存[key]
            if key in self.过期时间:
                del self.过期时间[key]
            return True

    def clear(self, pattern=None):
        with self.锁:
            if pattern is None:
                self.缓存.clear()
                self.过期时间.clear()
                return True

            keys_to_delete = [k for k in self.缓存.keys() if pattern in k]
            for key in keys_to_delete:
                self.delete(key)
            return True

    def 清理过期缓存(self):
        with self.锁:
            当前时间 = time.time()
            过期键 = [k for k, v in self.过期时间.items() if v <= 当前时间]
            for key in 过期键:
                self.delete(key)
            return len(过期键)

    def 启动清理线程(self, 间隔=None):  # 使用配置项设置清理间隔
        if 间隔 is None:
            间隔 = 免费账号模块配置['缓存清理间隔秒数']
        def 清理任务():
            while True:
                time.sleep(间隔)
                try:
                    清理数量 = self.清理过期缓存()
                    if 清理数量 > 0:
                        logging.debug(f"缓存清理: 已删除 {清理数量} 个过期项")
                except Exception as e:
                    logging.error(f"缓存清理任务出错: {e}", exc_info=True)

        清理线程 = threading.Thread(target=清理任务, name="CacheCleanupThread", daemon=True)
        清理线程.start()
        logging.info(f"缓存定期清理任务已启动，清理间隔: {间隔}秒。")

# 数据库连接管理器类
class 数据库连接管理器:
    def __init__(self, db_config, 缓存管理器实例=None):
        self.db_config = db_config
        self.缓存 = 缓存管理器实例
        try:
            self.pool = pooling.MySQLConnectionPool(**self.db_config)
            logging.info(f"数据库连接池 '{db_config['pool_name']}' 初始化成功")
        except Exception as e:
            logging.error(f"数据库连接池 '{db_config['pool_name']}' 初始化失败: {e}", exc_info=True)
            raise

    def get_connection(self):
        try:
            return self.pool.get_connection()
        except Exception as e:
            logging.error(f"获取数据库连接失败: {e}", exc_info=True)
            raise

    def execute_query(self, query, params=None, cache_key=None, cache_ttl_seconds=None):
        """执行查询并返回所有结果"""
        # 如果启用缓存且缓存中有结果，直接返回
        if cache_key and self.缓存:
            cached_result = self.缓存.get(cache_key)
            if cached_result is not None:
                return cached_result

        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, params)
            result = cursor.fetchall()

            # 如果启用缓存，将结果存入缓存
            if cache_key and self.缓存:
                self.缓存.set(cache_key, result, cache_ttl_seconds)

            return result
        except Exception as e:
            logging.error(f"执行查询失败: {e}, 查询: {query}, 参数: {params}", exc_info=True)
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def execute_single_query(self, query, params=None, cache_key=None, cache_ttl_seconds=None):
        """执行查询并返回单个结果"""
        results = self.execute_query(query, params, cache_key, cache_ttl_seconds)
        return results[0] if results else None

    def execute_update(self, query, params=None, clear_cache_keys=None):
        """执行更新操作"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            affected_rows = cursor.rowcount

            # 如果指定了要清除的缓存键，则清除
            if clear_cache_keys and self.缓存:
                if isinstance(clear_cache_keys, list):
                    for key in clear_cache_keys:
                        self.缓存.delete(key)
                else:
                    self.缓存.delete(clear_cache_keys)

            return affected_rows
        except Exception as e:
            logging.error(f"执行更新失败: {e}, 查询: {query}, 参数: {params}", exc_info=True)
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

# 获取真实客户端IP
def get_real_client_ip(request_obj):
    """
    获取真实客户端IP地址
    安全修复：不再信任可能被伪造的HTTP头部，直接使用Flask的remote_addr
    检测到IP伪造尝试时直接拒绝请求
    """
    real_ip = request_obj.remote_addr

    # 检测可能被伪造的头部信息
    x_forwarded_for = request_obj.headers.get('X-Forwarded-For')
    x_real_ip = request_obj.headers.get('X-Real-IP')

    if x_forwarded_for or x_real_ip:
        logging.warning(f"检测到IP伪造尝试，拒绝请求 - 真实IP: {real_ip}, X-Forwarded-For: {x_forwarded_for}, X-Real-IP: {x_real_ip}")
        # 抛出异常，阻止继续处理请求
        raise ValueError("检测到IP伪造尝试，请求被拒绝")

    return real_ip

# 创建Flask应用
app = Flask(__name__)

# 初始化IP限制表结构
def 初始化IP限制表():
    """初始化IP限制表结构，确保包含所需字段"""
    try:
        # 临时创建数据库连接来检查表结构
        temp_conn = mysql.connector.connect(**数据库配置)
        cursor = temp_conn.cursor()

        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE '试用账号IP'")
        table_exists = cursor.fetchone()

        if table_exists:
            logging.info("试用账号IP表已存在，检查表结构...")

            # 获取现有表结构
            cursor.execute("DESCRIBE 试用账号IP")
            existing_columns = {}
            for row in cursor.fetchall():
                if len(row) >= 2:
                    existing_columns[str(row[0])] = str(row[1])

            # 需要的字段定义
            required_columns = {
                '首次登录时间': 'datetime DEFAULT NULL',
                '最近登录时间': 'datetime DEFAULT NULL',
                '获取日期': 'datetime DEFAULT NULL',
                '今日获取次数': 'int DEFAULT 0',
                'IP是否被禁用': 'int DEFAULT 0',
                '访问时间记录': 'text DEFAULT NULL'  # 用于记录最近的访问时间，检测同秒访问
            }

            # 检查并添加缺失的字段
            for column_name, column_definition in required_columns.items():
                if column_name not in existing_columns:
                    try:
                        alter_sql = f"ALTER TABLE 试用账号IP ADD COLUMN `{column_name}` {column_definition}"
                        cursor.execute(alter_sql)
                        logging.info(f"成功添加字段: {column_name}")
                    except Exception as e:
                        logging.warning(f"添加字段 {column_name} 失败: {e}")
        else:
            logging.info("试用账号IP表不存在，创建新表...")

            # 创建新表
            create_table_sql = """
            CREATE TABLE `试用账号IP` (
                `试用IP` varchar(45) NOT NULL,
                `首次登录时间` datetime DEFAULT NULL,
                `最近登录时间` datetime DEFAULT NULL,
                `获取日期` datetime DEFAULT NULL,
                `今日获取次数` int DEFAULT 0,
                `IP是否被禁用` int DEFAULT 0,
                `访问时间记录` text DEFAULT NULL,
                PRIMARY KEY (`试用IP`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            cursor.execute(create_table_sql)
            logging.info("成功创建试用账号IP表")

        cursor.close()
        temp_conn.close()
        logging.info("IP限制表结构检查完成")
        return True

    except Exception as e:
        logging.error(f"初始化IP限制表失败: {e}", exc_info=True)
        return False

# 创建缓存和数据库连接实例
缓存实例 = 缓存管理器(默认过期时间=免费账号模块配置['设备信息缓存秒数'])  # 使用配置项
数据库实例 = 数据库连接管理器(数据库配置, 缓存实例)

# 恶意IP防护配置
恶意防护配置 = {
    # 简化配置：同秒访问检测阈值3次自动永久禁用IP，错误请求1次永久封禁
    # 注意：同秒访问检测阈值在免费账号模块配置中定义
}

# 初始化IP限制表结构
try:
    初始化IP限制表()
except Exception as e:
    logging.warning(f"IP限制表初始化警告: {e}")  # 不阻止服务启动

# 免费账号管理器类
class 免费账号管理器:
    def __init__(self, 数据库管理器, 缓存管理器_instance):
        self.数据库 = 数据库管理器
        self.缓存 = 缓存管理器_instance

    def 检查系统配置(self):
        try:
            配置 = self.数据库.execute_single_query(
                "SELECT 开启免费号池, 网络版本号_Windows, 网络版本号_Mac FROM cursorpro ORDER BY id DESC LIMIT 1",
                cache_key="系统配置_全局", cache_ttl_seconds=免费账号模块配置['系统配置缓存秒数']  # 使用配置项
            )
            if 配置:
                return 配置
            return {'开启免费号池': 0, '网络版本号_Windows': '', '网络版本号_Mac': ''}
        except Exception as e:
            logging.error(f"检查免费系统配置失败: {e}", exc_info=True)
            return {'开启免费号池': 0, '网络版本号_Windows': '', '网络版本号_Mac': ''}

    def 检查获取频率(self, 标识):
        return True, None

    def 更新获取时间(self, 标识):
        pass

    def 删除异常账号(self, 邮箱, 访问令牌, 刷新令牌, 原因, 用户标识=None):
        try:
            self.数据库.execute_update(
                "INSERT IGNORE INTO 已删除邮箱(邮箱, 访问令牌, 刷新令牌, 创建时间, 用户标识, 删除原因) VALUES (%s, %s, %s, NOW(), %s, %s)",
                params=(邮箱, 访问令牌, 刷新令牌, 用户标识, 原因)
            )
            self.数据库.execute_update("DELETE FROM 邮箱免费 WHERE 邮箱 = %s", params=(邮箱,))
            logging.info(f"已删除免费账号: {邮箱[:5]}*** 原因: {原因} 用户: {用户标识 or '未知'}")
        except Exception as e:
            logging.error(f"删除免费账号失败: {str(e)[:100]}", exc_info=True)

    def 获取账号(self, 用户标识):
        try:
            频率检查结果, 频率错误消息 = self.检查获取频率(用户标识)
            if not 频率检查结果:
                logging.info(f"用户 {用户标识} 获取免费账号频率限制: {频率错误消息}")
                return False, None, None, None, 频率错误消息

            邮箱信息 = self.数据库.execute_single_query(
                "SELECT 邮箱, 访问令牌, 刷新令牌 FROM 邮箱免费 WHERE 邮箱 IS NOT NULL AND 访问令牌 IS NOT NULL AND 刷新令牌 IS NOT NULL AND 创建时间 >= '2023-05-16 00:00:00' ORDER BY 创建时间 DESC LIMIT 1"
            )

            if not 邮箱信息:
                logging.error(f"无可用免费账号 (用户标识: {用户标识})")
                return False, None, None, None, '今日免费额度紧张已用完，立即升级获取独享无限额度？'

            邮箱 = 邮箱信息['邮箱']
            访问令牌 = 邮箱信息['访问令牌']
            刷新令牌 = 邮箱信息['刷新令牌']

            # 将邮箱信息写入到已删除邮箱表中
            self.数据库.execute_update(
                "INSERT IGNORE INTO 已删除邮箱(邮箱, 访问令牌, 刷新令牌, 创建时间, 用户标识, 删除原因) VALUES (%s, %s, %s, NOW(), %s, %s)",
                params=(邮箱, 访问令牌, 刷新令牌, 用户标识, 免费账号模块配置['删除原因_正常使用'])
            )

            # 从邮箱免费表中删除
            self.数据库.execute_update("DELETE FROM 邮箱免费 WHERE 邮箱 = %s", params=(邮箱,))
            self.更新获取时间(用户标识)

            # 生成随机虚假邮箱
            虚假邮箱 = 生成虚假邮箱()
            logging.info(f"用户 {用户标识} 成功获取免费账号: {邮箱}, 返回虚假账号: {虚假邮箱}")
            return True, 虚假邮箱, 访问令牌, 刷新令牌, None

        except Exception as e:
            logging.error(f"获取免费邮箱账号出错: {e}", exc_info=True)
            return False, None, None, None, f"获取免费账号系统错误: {e}"

    def 检查同秒访问并禁用(self, IP, 当前时间):
        """检查同一秒内的访问次数，如果超过3次则自动禁用IP"""
        try:
            当前秒 = 当前时间.strftime('%Y-%m-%d %H:%M:%S')

            # 查询IP记录
            ip_info = self.数据库.execute_single_query(
                "SELECT 访问时间记录, IP是否被禁用 FROM 试用账号IP WHERE 试用IP=%s",
                params=(IP,)
            )

            if ip_info:
                # 检查是否已被禁用
                if ip_info.get('IP是否被禁用', 0) != 0:
                    logging.warning(f"检测到异常访问行为: IP {IP} 已被禁用")
                    return False, "检测到异常行为,请联系管理员"

                访问时间记录 = ip_info.get('访问时间记录', '') or ''

                # 解析访问时间记录（格式：时间1,时间2,时间3...）
                访问时间列表 = []
                if 访问时间记录:
                    try:
                        访问时间列表 = 访问时间记录.split(',')
                    except:
                        访问时间列表 = []

                # 统计当前秒的访问次数
                当前秒访问次数 = 访问时间列表.count(当前秒)

                # 添加当前访问时间
                访问时间列表.append(当前秒)

                # 获取检测阈值配置
                检测阈值 = 免费账号模块配置['同秒访问检测阈值']

                # 只保留最近N次访问记录，足够检测同秒访问（保留数量等于检测阈值）
                if len(访问时间列表) > 检测阈值:
                    访问时间列表 = 访问时间列表[-检测阈值:]

                # 检查是否需要禁用（同一秒访问达到阈值）
                if 当前秒访问次数 >= (检测阈值 - 1):  # 加上当前这次就达到阈值
                    # 自动禁用IP - 同时设置数据库标志和缓存永久封禁
                    self.数据库.execute_update(
                        "UPDATE 试用账号IP SET IP是否被禁用=1, 访问时间记录=%s, 最近登录时间=%s WHERE 试用IP=%s",
                        params=(','.join(访问时间列表), 当前时间, IP)
                    )
                    # 同时设置缓存永久封禁
                    permanent_ban_key = f"permanent_ban:{IP}"
                    缓存实例.set(permanent_ban_key, True, 86400 * 365)  # 1年
                    logging.warning(f"检测到异常访问行为: IP {IP} 在同一秒内访问{当前秒访问次数 + 1}次，已自动永久禁用")
                    return False, "检测到你的操作存在异常行为!!!"
                else:
                    # 更新访问时间记录
                    self.数据库.execute_update(
                        "UPDATE 试用账号IP SET 访问时间记录=%s, 最近登录时间=%s WHERE 试用IP=%s",
                        params=(','.join(访问时间列表), 当前时间, IP)
                    )
            else:
                # 新IP，创建记录
                self.数据库.execute_update(
                    "INSERT INTO 试用账号IP (试用IP, 首次登录时间, 最近登录时间, 获取日期, 今日获取次数, IP是否被禁用, 访问时间记录) VALUES (%s, %s, %s, %s, 0, 0, %s)",
                    params=(IP, 当前时间, 当前时间, 当前时间, 当前秒)
                )

            return True, "访问检查通过"

        except Exception as e:
            logging.error(f"同秒访问检查失败: {e}", exc_info=True)
            return False, f"访问检查出错: {str(e)}"

    def 检查IP限制(self, IP):
        """检查IP的今日获取次数限制（不增加次数）"""
        try:
            now = datetime.datetime.now()
            today = now.date()

            # 首先检查同秒访问并可能禁用IP
            同秒检查结果, 同秒消息 = self.检查同秒访问并禁用(IP, now)
            if not 同秒检查结果:
                return False, 同秒消息

            # 查询IP记录
            ip_info = self.数据库.execute_single_query(
                "SELECT 首次登录时间, 最近登录时间, 获取日期, 今日获取次数, IP是否被禁用 FROM 试用账号IP WHERE 试用IP=%s",
                params=(IP,)
            )

            if ip_info:
                # 再次检查是否被禁用（可能在同秒检查中被禁用）
                if ip_info.get('IP是否被禁用', 0) != 0:
                    logging.warning(f"检测到异常访问行为: IP {IP} 已被禁用")
                    return False, "检测到你的操作存在异常行为!!!"

                # 检查IP星期几循环限制（如果启用）
                if 免费账号模块配置['启用IP天数循环限制']:
                    首次登录时间 = ip_info.get('首次登录时间')
                    if 首次登录时间:
                        # 解析首次登录时间
                        if isinstance(首次登录时间, datetime.datetime):
                            首次登录日期 = 首次登录时间.date()
                        else:
                            try:
                                首次登录日期 = datetime.datetime.strptime(str(首次登录时间), '%Y-%m-%d %H:%M:%S').date()
                            except:
                                首次登录日期 = today  # 如果解析失败，当作今天是第一天

                        # 检查是否是首次使用（首次登录当天）
                        if 首次登录日期 == today:
                            logging.info(f"IP {IP} 首次使用，允许获取账号")
                        else:
                            # 非首次使用，检查星期几限制
                            今天星期几 = today.weekday()  # 0=周一, 1=周二, 2=周三, 3=周四, 4=周五, 5=周六, 6=周日

                            # 所有天都不能获取（周一到周日全关闭）
                            if 今天星期几 in [0, 1, 2, 3, 4, 5, 6]:  # 周一至周日
                                星期名称 = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][今天星期几]
                                logging.info(f"IP {IP} 星期几限制: 今天是{星期名称}，不允许获取")
                                return False, "今日免费额度紧张已用完，立即升级获取独享无限额度？"

                获取日期 = ip_info.get('获取日期')
                今日获取次数 = ip_info.get('今日获取次数', 0) or 0

                # 检查是否是新的一天，如果是则重置获取次数
                if 获取日期:
                    if isinstance(获取日期, datetime.datetime):
                        获取日期_date = 获取日期.date()
                    else:
                        try:
                            获取日期_date = datetime.datetime.strptime(str(获取日期), '%Y-%m-%d %H:%M:%S').date()
                        except:
                            获取日期_date = datetime.date(1970, 1, 1)

                    # 如果是新的一天，重置获取次数
                    if 获取日期_date < today:
                        今日获取次数 = 0

                # 检查今日获取次数是否超过限制
                最大获取次数 = 免费账号模块配置['IP每日最大获取次数']
                if 今日获取次数 >= 最大获取次数:
                    logging.warning(f"IP {IP} 今日获取次数已达上限: {今日获取次数}/{最大获取次数}")
                    return False, "今日免费额度紧张已用完，立即升级获取独享无限额度？"

            return True, "IP检查通过"

        except Exception as e:
            logging.error(f"IP限制检查失败: {e}", exc_info=True)
            return False, f"IP检查出错: {str(e)}"

    def 增加IP获取次数(self, IP):
        """增加IP的今日获取次数（只在成功获取账号后调用）"""
        try:
            now = datetime.datetime.now()
            today = now.date()

            # 查询IP记录
            ip_info = self.数据库.execute_single_query(
                "SELECT 获取日期, 今日获取次数 FROM 试用账号IP WHERE 试用IP=%s",
                params=(IP,)
            )

            if ip_info:
                获取日期 = ip_info.get('获取日期')
                获取日期_date = None

                if 获取日期:
                    if isinstance(获取日期, datetime.datetime):
                        获取日期_date = 获取日期.date()
                    else:
                        try:
                            获取日期_date = datetime.datetime.strptime(str(获取日期), '%Y-%m-%d %H:%M:%S').date()
                        except:
                            获取日期_date = datetime.date(1970, 1, 1)

                # 更新IP记录
                if 获取日期_date and 获取日期_date < today:
                    # 新的一天，重置获取次数为1
                    self.数据库.execute_update(
                        "UPDATE 试用账号IP SET 最近登录时间=%s, 获取日期=%s, 今日获取次数=1 WHERE 试用IP=%s",
                        params=(now, now, IP)
                    )
                else:
                    # 同一天，递增获取次数
                    self.数据库.execute_update(
                        "UPDATE 试用账号IP SET 最近登录时间=%s, 获取日期=%s, 今日获取次数=今日获取次数+1 WHERE 试用IP=%s",
                        params=(now, now, IP)
                    )
            else:
                # 新IP记录，设置获取次数为1
                self.数据库.execute_update(
                    "UPDATE 试用账号IP SET 获取日期=%s, 今日获取次数=1 WHERE 试用IP=%s",
                    params=(now, IP)
                )

            logging.info(f"IP {IP} 获取次数已增加")
            return True

        except Exception as e:
            logging.error(f"增加IP获取次数失败: {e}", exc_info=True)
            return False

    def 检查是否需要虚假获取(self, 机器码, 当前时间):
        """
        检查是否需要虚假获取（基于试用账号表的上次获取时间和虚假免费时长配置）
        """
        try:
            # 从数据库获取虚假免费时长配置
            虚假免费时长_分钟 = self.获取虚假免费时长()
            if 虚假免费时长_分钟 <= 0:
                logging.debug(f"虚假免费时长配置为 {虚假免费时长_分钟} 分钟，禁用虚假获取")
                return False

            # 查询设备的上次获取时间
            device_info = self.数据库.execute_single_query(
                "SELECT 上次获取时间, 今日获取次数, 获取日期 FROM 试用账号 WHERE 机器码=%s",
                params=(机器码,)
            )

            if not device_info:
                logging.debug(f"设备 {机器码} 无获取记录，不需要虚假获取")
                return False

            上次获取时间 = device_info.get('上次获取时间')
            今日获取次数 = device_info.get('今日获取次数', 0) or 0

            # 如果今日获取次数为0，说明今天还没有成功获取过，不需要虚假获取
            if 今日获取次数 == 0:
                logging.debug(f"设备 {机器码} 今日获取次数为0，不需要虚假获取")
                return False

            # 如果没有上次获取时间，不需要虚假获取
            if not 上次获取时间:
                logging.debug(f"设备 {机器码} 无上次获取时间记录，不需要虚假获取")
                return False

            # 计算时间差
            时间差 = 当前时间 - 上次获取时间
            虚假时长_秒 = 虚假免费时长_分钟 * 60  # 转换为秒

            # 如果在虚假时长内，则需要虚假获取
            if 时间差.total_seconds() <= 虚假时长_秒:
                logging.info(f"设备 {机器码} 距离上次获取 {时间差.total_seconds():.1f} 秒，在{虚假免费时长_分钟}分钟内，需要虚假获取")
                return True
            else:
                logging.debug(f"设备 {机器码} 距离上次获取 {时间差.total_seconds():.1f} 秒，超过{虚假免费时长_分钟}分钟，不需要虚假获取")
                return False

        except Exception as e:
            logging.error(f"检查是否需要虚假获取时出错: {e}")
            return False

    def 获取虚假免费时长(self):
        """
        从数据库获取虚假免费时长配置（分钟）
        """
        try:
            # 从cursorpro表获取虚假免费时长配置（不使用缓存，立即生效）
            配置 = self.数据库.execute_single_query(
                "SELECT 虚假免费时长 FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            if 配置 and '虚假免费时长' in 配置:
                虚假时长 = 配置['虚假免费时长']
                # 确保是有效的整数
                if isinstance(虚假时长, int) and 虚假时长 >= 0:
                    logging.debug(f"获取到虚假免费时长配置: {虚假时长} 分钟")
                    return 虚假时长
                else:
                    logging.warning(f"虚假免费时长配置无效: {虚假时长}，使用默认值5分钟")
                    return 5
            else:
                logging.warning("未找到虚假免费时长配置，使用默认值5分钟")
                return 5

        except Exception as e:
            logging.error(f"获取虚假免费时长配置失败: {e}，使用默认值5分钟")
            return 5

    def 生成虚假账号信息(self):
        """生成虚假账号信息"""
        # 生成虚假邮箱：8-12位随机字符@配置的邮箱后缀
        用户名 = ''.join(random.choices(string.ascii_lowercase + string.digits, k=random.randint(8, 12)))
        虚假邮箱 = f"{用户名}@{免费账号模块配置['邮箱后缀']}"

        # 生成虚假令牌：固定前缀 + 随机字符，保持长度一致
        前缀 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
        中间部分 = ''.join(random.choices(string.ascii_letters + string.digits + '_-', k=200))
        后缀 = ''.join(random.choices(string.ascii_letters + string.digits + '_-', k=43))
        虚假访问令牌 = f"{前缀}{中间部分}.{后缀}"
        虚假刷新令牌 = f"{前缀}{中间部分}.{后缀}"

        return {"邮箱": 虚假邮箱, "访问令牌": 虚假访问令牌, "刷新令牌": 虚假刷新令牌}

    def 验证试用账号(self, 机器码, 真实IP):
        try:
            now = datetime.datetime.now()
            today = now.date()
            缓存键_设备信息 = f"免费设备信息:{机器码}"
            # 添加一个锁定键，用于防止并发问题
            缓存键_设备锁定 = f"免费设备锁定:{机器码}"

            # 安全修复：只使用真实IP，不再接受可能被伪造的IP参数
            logging.info(f"验证试用账号 - 机器码: {机器码}, 真实IP: {真实IP}")

            # 先检查是否需要虚假获取（虚假获取不需要锁定，因为不涉及数据库操作）
            虚假获取检查结果 = self.检查是否需要虚假获取(机器码, now)
            if 虚假获取检查结果:
                logging.info(f"设备 {机器码} 触发虚假获取条件，返回虚假成功，不更新数据库")
                # 生成虚假账号信息并返回
                虚假账号信息 = self.生成虚假账号信息()
                return True, "验证成功", 虚假账号信息["邮箱"], 虚假账号信息["访问令牌"], 虚假账号信息["刷新令牌"], 0, True  # 最后一个参数表示是虚假获取

            # 尝试获取锁，防止并发操作同一设备（仅对真实获取）
            if self.缓存.get(缓存键_设备锁定):
                logging.warning(f"设备 {机器码} 正在处理中，请稍后再试")
                return False, "系统正在处理您的请求，请稍后再试", None, None, None, 0, False

            # 设置锁定标志，防止死锁
            锁定超时 = 免费账号模块配置['设备锁定超时秒数']
            self.缓存.set(缓存键_设备锁定, True, 锁定超时)

            try:
                # 1. 首先检查IP限制（使用真实IP进行检查，防止伪造）
                if 免费账号模块配置['启用IP限制检查']:
                    ip_check_result, ip_message = self.检查IP限制(真实IP)
                    if not ip_check_result:
                        logging.warning(f"真实IP {真实IP} 限制检查失败: {ip_message}")
                        return False, ip_message, None, None, None, 0, False

                # 2. 检查设备信息 - 直接从数据库获取最新信息，不使用缓存
                device_info = self.数据库.execute_single_query(
                    "SELECT 禁用机器码, 获取次数, 今日获取次数, 获取日期 FROM 试用账号 WHERE 机器码=%s",
                    params=(机器码,)
                )

                # 检查设备是否被禁用（如果启用了设备限制检查）
                if 免费账号模块配置['启用设备限制检查'] and device_info and device_info.get('禁用机器码', 0) != 0:
                    logging.warning(f"免费设备机器码被禁用: {机器码}")
                    return False, "检测到你的操作存在异常行为!!!", None, None, None, 0, False

                # 3. 检查 IP 限制 - 同一真实IP下的设备数量限制（防止伪造IP绕过限制）
                ip_设备数 = self.数据库.execute_single_query(
                    "SELECT COUNT(DISTINCT 机器码) as count FROM 试用账号 WHERE 当前IP=%s AND DATE(获取日期)=CURDATE()",
                    params=(真实IP,)
                )
                ip_设备数_count = ip_设备数.get('count', 0) if ip_设备数 else 0

                if ip_设备数_count > 免费账号模块配置['IP每日最大设备数']:
                    logging.warning(f"真实IP {真实IP} 今日设备数已达上限: {ip_设备数_count}/{免费账号模块配置['IP每日最大设备数']}")
                    return False, "今日免费额度紧张已用完，立即升级获取独享无限额度？", None, None, None, 0, False

                # 4. 检查设备每日获取次数限制
                if device_info and 免费账号模块配置['启用设备限制检查']:
                    获取日期 = device_info.get('获取日期')
                    获取日期_date = None

                    # 获取日期是datetime类型，转换为date类型进行比较
                    if 获取日期:
                        if isinstance(获取日期, datetime.datetime):
                            获取日期_date = 获取日期.date()
                        else:
                            # 以防万一处理其他可能的类型
                            logging.warning(f"获取日期类型异常: {type(获取日期)}, 值: {获取日期}")
                            try:
                                if isinstance(获取日期, str):
                                    获取日期_date = datetime.datetime.strptime(获取日期, '%Y-%m-%d %H:%M:%S').date()
                                else:
                                    获取日期_date = 获取日期
                            except Exception as e:
                                logging.error(f"转换获取日期失败: {e}")
                                获取日期_date = datetime.date(1970, 1, 1)  # 使用一个很早的日期作为默认值

                    今日获取次数 = device_info.get('今日获取次数', 0) or 0

                    # 如果是新的一天，重置今日获取次数
                    if 获取日期_date and 获取日期_date < today:
                        今日获取次数 = 0

                    if 今日获取次数 >= 免费账号模块配置['设备每日最大获取账号数']:
                        logging.warning(f"设备 {机器码} 今日获取次数已达上限: {今日获取次数}/{免费账号模块配置['设备每日最大获取账号数']}")
                        # 动态计算总额度：设备每日最大获取账号数 * 每个账号的额度
                        # 每个账号额度 = 50  # 每个账号提供的额度
                        # 总额度 = 免费账号模块配置['设备每日最大获取账号数'] * 每个账号额度
                        return False, f"今日免费额度紧张已用完，立即升级获取独享无限额度？", None, None, None, 今日获取次数, False

                获取成功, 邮箱, 访问令牌, 刷新令牌, 错误类型 = self.获取账号(机器码)

                if 获取成功:
                    # 只有在成功获取账号后才增加IP获取次数
                    self.增加IP获取次数(真实IP)

                    # 使用原子操作更新数据库中的计数器
                    if not device_info:
                        # 新设备，首次获取 - 使用INSERT ... ON DUPLICATE KEY UPDATE确保原子性
                        # 注意：当前IP字段记录真实IP，防止伪造
                        self.数据库.execute_update(
                            """
                            INSERT INTO 试用账号
                                (机器码, 创建时间, 最近登录, 获取次数, 禁用机器码, 当前IP, 今日获取次数, 获取日期, 上次获取时间)
                            VALUES
                                (%s, %s, %s, 1, 0, %s, 1, %s, %s)
                            ON DUPLICATE KEY UPDATE
                                最近登录 = VALUES(最近登录),
                                当前IP = VALUES(当前IP),
                                上次获取时间 = VALUES(上次获取时间)
                            """,
                            params=(机器码, now, now, 真实IP, now, now)
                        )
                    else:
                        # 已有设备，使用原子操作更新信息
                        获取次数 = (device_info.get('获取次数', 0) or 0) + 1

                        # 计算今日获取次数
                        if 获取日期_date and 获取日期_date < today:
                            # 如果是新的一天，重置今日获取次数为1
                            # 注意：当前IP字段记录真实IP，防止伪造
                            self.数据库.execute_update(
                                """
                                UPDATE 试用账号
                                SET 最近登录=%s, 当前IP=%s, 获取次数=%s, 今日获取次数=1, 获取日期=%s, 上次获取时间=%s
                                WHERE 机器码=%s
                                """,
                                params=(now, 真实IP, 获取次数, now, now, 机器码)
                            )
                        else:
                            # 同一天，使用原子操作递增今日获取次数
                            # 注意：当前IP字段记录真实IP，防止伪造
                            self.数据库.execute_update(
                                """
                                UPDATE 试用账号
                                SET 最近登录=%s, 当前IP=%s, 获取次数=%s, 今日获取次数=今日获取次数+1, 获取日期=%s, 上次获取时间=%s
                                WHERE 机器码=%s
                                """,
                                params=(now, 真实IP, 获取次数, now, now, 机器码)
                            )

                    # 获取更新后的今日获取次数
                    updated_info = self.数据库.execute_single_query(
                        "SELECT 今日获取次数 FROM 试用账号 WHERE 机器码=%s",
                        params=(机器码,)
                    )
                    实际今日获取次数 = updated_info.get('今日获取次数', 1) if updated_info else 1

                    # 更新缓存
                    self.缓存.delete(缓存键_设备信息)

                    # 日志记录详细信息供管理员查看
                    logging.info(f"免费账号分配成功 - 机器码: {机器码}, 真实邮箱: {邮箱}, 真实IP: {真实IP}")
                    # 给客户端返回通用成功消息，不暴露真实邮箱
                    return True, "验证成功", 邮箱, 访问令牌, 刷新令牌, 实际今日获取次数, False  # 最后一个参数表示不是虚假获取
                else:
                    # 获取账号失败，不增加IP获取次数
                    logging.warning(f"用户 {机器码} 获取免费账号失败: {错误类型}，IP获取次数未增加")
                    return False, 错误类型, None, None, None, 0, False
            finally:
                # 无论如何都要释放锁
                self.缓存.delete(缓存键_设备锁定)

        except Exception as e:
            # 确保发生异常时也释放锁
            try:
                self.缓存.delete(f"免费设备锁定:{机器码}")
            except:
                pass
            logging.error(f"验证免费试用账号失败 - 发生意外错误 ({机器码}): {e}", exc_info=True)
            return False, f"免费服务服务器错误: {str(e)}", None, None, None, 0, False

# 创建免费账号管理器实例
免费账号管理器_实例 = 免费账号管理器(数据库管理器=数据库实例, 缓存管理器_instance=缓存实例)

# IP频率限制和黑名单管理
def 检查IP频率限制(ip_address, is_error_request=False):
    """简化的IP检查：检查永久封禁状态，错误请求1次永久封禁"""
    try:
        # 检查是否在永久黑名单中
        permanent_ban_key = f"permanent_ban:{ip_address}"
        if 缓存实例.get(permanent_ban_key):
            logging.warning(f"永久封禁IP访问: {ip_address}")
            return False, "系统检测到异常行为，相关信息已记录"

        # 如果是错误请求，直接永久封禁
        if is_error_request:
            # 永久封禁
            缓存实例.set(permanent_ban_key, True, 86400 * 365)  # 1年
            logging.error(f"IP因错误请求被永久封禁: {ip_address}")
            return False, "系统检测到异常行为，相关信息已记录"

        return True, None

    except Exception as e:
        logging.error(f"IP频率检查失败: {e}", exc_info=True)
        return True, None  # 出错时不阻止正常请求

# 恶意请求检测和防护
def 检测恶意请求():
    """检测并阻止恶意请求，减少服务器资源浪费"""

    ip_address = request.remote_addr

    # 0. 首先检查IP频率限制
    freq_check, freq_msg = 检查IP频率限制(ip_address)
    if not freq_check:
        return jsonify({'success': False, 'message': freq_msg}), 429  # Too Many Requests

    # 1. 检查Content-Type（允许charset参数）
    content_type = request.content_type
    if not content_type or not content_type.startswith('application/json'):
        logging.warning(f"恶意探测 - 错误Content-Type: {content_type}, IP: {ip_address}, UA: {request.user_agent.string[:100]}")
        # 记录为错误请求
        检查IP频率限制(ip_address, is_error_request=True)
        return jsonify({'success': False, 'message': '系统检测到异常行为，相关信息已记录'}), 400

    # 2. 检查User-Agent（放宽检查，只检测明显的扫描工具）
    user_agent = request.user_agent.string or ""

    # 3. 检查明显的恶意扫描工具特征（排除可能的正常客户端）
    suspicious_ua_patterns = [
        'python-requests', 'curl/', 'wget/', 'scanner', 'nmap', 'sqlmap',
        'nikto', 'dirb', 'gobuster', 'ffuf', 'burp', 'zap', 'masscan'
    ]

    user_agent_lower = user_agent.lower()
    for pattern in suspicious_ua_patterns:
        if pattern in user_agent_lower:
            logging.warning(f"恶意探测 - 检测到扫描工具: {user_agent}, IP: {ip_address}")
            检查IP频率限制(ip_address, is_error_request=True)
            return jsonify({'success': False, 'message': '系统检测到异常行为，相关信息已记录'}), 400

    # 4. 检查请求大小（防止大数据攻击）
    if request.content_length and request.content_length > 10240:  # 10KB限制
        logging.warning(f"恶意探测 - 请求过大: {request.content_length}字节, IP: {ip_address}")
        检查IP频率限制(ip_address, is_error_request=True)
        return jsonify({'success': False, 'message': '系统检测到异常行为，相关信息已记录'}), 400

    return None

# 定义路由
@app.route('/api/account', methods=['POST'])
def 免费账号管理_路由():
    # 首先进行恶意请求检测
    malicious_check = 检测恶意请求()
    if malicious_check:
        return malicious_check

    try:
        data = request.get_json()
        if not data:
            logging.warning(f"空JSON数据 - IP: {request.remote_addr}")
            return jsonify({'success': False, 'message': '系统检测到异常行为，相关信息已记录'}), 400

        操作类型 = data.get('operation', '')
        if 操作类型 == 'verify_trial':
            return _验证试用账号接口_逻辑(data, 免费账号管理器_实例)
        else:
            return jsonify({'success': False, 'message': '未知的操作类型或此服务不支持'}), 400
    except Exception as e:
        # 详细错误信息记录到日志供管理员查看
        logging.error(f"免费API请求处理失败: {e}", exc_info=True)
        # 给客户端返回通用错误消息，不暴露系统内部信息
        return jsonify({'success': False, 'message': '服务暂时不可用，请稍后再试'}), 500

def _验证试用账号接口_逻辑(data, acc_manager: 免费账号管理器):
    if not data:
        return jsonify({'success': False, 'message': '缺少请求数据', 'trial_count': 0}), 400

    必填参数 = ['machine_code', 'version', 'platform']  # IP参数改为可选，兼容老版本
    缺少参数 = [param for param in 必填参数 if param not in data]
    if 缺少参数:
        # 日志中记录真实的缺少参数信息，便于调试
        logging.warning(f"免费账号验证请求缺少参数: {', '.join(缺少参数)}")
        # 给客户端返回通用错误消息，避免暴露具体参数名称
        return jsonify({'success': False, 'message': '请求参数不完整', 'trial_count': 0}), 400

    try:
        系统配置 = acc_manager.检查系统配置()
        开启免费号池 = 系统配置.get('开启免费号池', 0)
        网络版本号_Windows = 系统配置.get('网络版本号_Windows', '')
        网络版本号_Mac = 系统配置.get('网络版本号_Mac', '')

        客户端版本 = data['version']
        平台 = data['platform']
        服务器版本 = 网络版本号_Windows if 平台 == 'Windows' else 网络版本号_Mac

        if 服务器版本 and 客户端版本 < 服务器版本:
            # 详细版本信息记录到日志供管理员查看
            logging.warning(f"客户端版本过低 - 客户端: {客户端版本}, 服务器要求: {服务器版本}, 平台: {平台}")
            # 给客户端返回更新提示，在消息中包含版本号
            return jsonify({'success': False, 'message': f'当前版本过低，请下载最新版本 {服务器版本}', 'trial_count': 0, 'need_update': True})

        if 开启免费号池 == 0:
            return jsonify({'success': False, 'message': '今日免费额度紧张已用完，立即升级获取独享无限额度？', 'trial_count': 0})

        机器码 = data['machine_code']

        # IP参数改为可选，兼容老版本 - 即使客户端传递了IP参数也不处理
        客户端IP = data.get('ip', '未提供')  # 可选参数，用于日志记录

        # 获取服务器端的真实IP地址（用于所有业务逻辑，防止伪造）
        try:
            真实IP = get_real_client_ip(request)
        except ValueError as e:
            # 检测到IP伪造尝试，直接拒绝请求
            logging.error(f"IP伪造检测失败，拒绝请求: {e}")
            return jsonify({'success': False, 'message': '系统检测到异常行为，相关信息已记录', 'trial_count': 0}), 400

        # 始终使用真实IP，不再信任客户端传递的IP参数（兼容老版本）
        # IP参数为可选参数，兼容老版本客户端
        if 客户端IP != '未提供':
            logging.info(f"客户端传递的IP: {客户端IP} (仅记录，不用于业务逻辑), 服务器获取的真实IP: {真实IP}")
        else:
            logging.info(f"客户端未传递IP参数 (兼容老版本), 服务器获取的真实IP: {真实IP}")

        # 记录请求日志
        logging.info(f"免费账号验证请求 - 机器码: {机器码}, 客户端IP: {客户端IP}, 真实IP: {真实IP}, 平台: {平台}, 版本: {客户端版本}")

        # 只传递真实IP，移除可能被伪造的IP参数
        result, message, 邮箱, 访问令牌, 刷新令牌, 免费已试用次数, 是虚假获取 = acc_manager.验证试用账号(机器码, 真实IP)

        if result:
            if 是虚假获取:
                # 虚假获取，返回成功但不包含账号信息
                logging.info(f"免费账号虚假获取 - 机器码: {机器码}, 真实IP: {真实IP}")
                return jsonify({'success': True, 'message': message, 'trial_count': 免费已试用次数, 'is_fake': True})
            else:
                # 真实获取成功
                logging.info(f"免费账号验证成功 - 机器码: {机器码}, 真实IP: {真实IP}, 已试用次数: {免费已试用次数}")
                账号信息 = {"邮箱": 邮箱, "访问令牌": 访问令牌, "刷新令牌": 刷新令牌}
                return jsonify({'success': True, 'message': message, 'trial_count': 免费已试用次数, 'account_info': 账号信息, 'is_fake': False})
        else:
            logging.info(f"免费账号验证失败 - 机器码: {机器码}, 真实IP: {真实IP}, 原因: {message}")
            return jsonify({'success': False, 'message': message, 'trial_count': 免费已试用次数 if isinstance(免费已试用次数, int) else 0})
    except Exception as e:
        # 检查是否是恶意请求导致的异常，减少日志垃圾
        error_str = str(e)
        if any(keyword in error_str for keyword in ['JSON', 'Content-Type', 'Media Type', 'Bad Request']):
            # 恶意请求导致的异常，只记录警告级别
            logging.warning(f"恶意请求导致异常 - IP: {request.remote_addr}, 错误: {error_str[:100]}")
            # 记录为错误请求
            检查IP频率限制(request.remote_addr, is_error_request=True)
        else:
            # 真正的系统错误，记录详细信息
            logging.error(f"验证免费试用账号接口逻辑失败: {e}", exc_info=True)

        # 给客户端返回通用错误消息，不暴露系统内部信息
        return jsonify({'success': False, 'message': '服务暂时不可用，请稍后再试', 'trial_count': 0}), 500

# 添加日志记录中间件
@app.before_request
def log_request_info():
    """在每个请求处理前记录请求信息。"""
    try:
        real_ip = get_real_client_ip(request)

        # 检测错误的HTTP方法（API探测行为）
        if request.endpoint == '免费账号管理_路由' and request.method != 'POST':
            logging.warning(f"API探测 - 错误HTTP方法: {request.method}, IP: {real_ip}, 路径: {request.full_path}")
            # 记录为恶意行为
            检查IP频率限制(real_ip, is_error_request=True)
            return jsonify({'success': False, 'message': '系统检测到异常行为，相关信息已记录'}), 405

        logging.info(f"收到请求: {request.method} {request.full_path} IP: {real_ip} User-Agent: {request.user_agent.string}")
    except ValueError as e:
        # IP伪造检测失败，记录并直接返回错误响应
        logging.error(f"请求被拒绝 - IP伪造检测: {e}")
        return jsonify({'success': False, 'message': '系统检测到异常行为，相关信息已记录'}), 400

@app.after_request
def log_response_info(response):
    """在每个请求处理后记录响应信息。"""
    try:
        real_ip = get_real_client_ip(request)
        logging.info(f"响应状态: {response.status_code} - {request.method} {request.full_path} - IP: {real_ip}")
    except ValueError:
        # IP伪造的请求在before_request中已经被拒绝，这里不需要再处理
        logging.info(f"响应状态: {response.status_code} - {request.method} {request.full_path} - IP: [伪造检测失败]")
    return response

# 管理员工具：解除IP封禁
def 解除IP封禁(ip_address):
    """管理员工具：解除指定IP的封禁状态"""
    try:
        # 清除临时封禁
        temp_ban_key = f"temp_ban:{ip_address}"
        缓存实例.delete(temp_ban_key)

        # 清除永久封禁
        permanent_ban_key = f"permanent_ban:{ip_address}"
        缓存实例.delete(permanent_ban_key)

        # 清除错误计数
        total_error_key = f"total_errors:{ip_address}"
        缓存实例.delete(total_error_key)

        logging.info(f"管理员解除IP封禁: {ip_address}")
        print(f"已解除IP {ip_address} 的所有封禁状态")
        return True
    except Exception as e:
        logging.error(f"解除IP封禁失败: {e}", exc_info=True)
        return False

# 管理员工具：批量解除封禁
def 批量解除封禁():
    """解除所有当前被误封的正常用户IP"""
    try:
        # 这些是从日志中看到的可能被误封的IP
        误封IP列表 = [
            "**************", "***************", "**************",
            "************", "**********", "************"
        ]

        for ip in 误封IP列表:
            解除IP封禁(ip)

        print(f"已批量解除 {len(误封IP列表)} 个IP的封禁状态")
        return True
    except Exception as e:
        logging.error(f"批量解除封禁失败: {e}", exc_info=True)
        return False

# 管理员工具：查看封禁状态
def 查看封禁状态():
    """管理员工具：查看当前封禁的IP列表"""
    try:
        import time
        current_time = time.time()

        print("=== 恶意IP防护状态 ===")
        print("简化配置信息:")
        print(f"  同秒访问检测阈值: {免费账号模块配置['同秒访问检测阈值']}次 (自动永久禁用IP)")
        print("  错误请求永久封禁次数: 1次")

        print(f"\n当前时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}")

        # 这里可以添加更多管理功能，比如查看缓存中的封禁IP
        # 由于缓存实例的限制，暂时只显示配置信息

        print("=== 防护状态检查完成 ===")
        return True
    except Exception as e:
        logging.error(f"查看封禁状态失败: {e}", exc_info=True)
        return False

# 配置验证函数
def 验证配置():
    """验证免费账号模块配置是否正确加载"""
    try:
        logging.info("=== 免费账号模块配置验证 ===")

        # 基础限制配置
        logging.info(f"IP每日最大设备数: {免费账号模块配置['IP每日最大设备数']}")
        logging.info(f"设备每日最大获取账号数: {免费账号模块配置['设备每日最大获取账号数']}")
        logging.info(f"IP每日最大获取次数: {免费账号模块配置['IP每日最大获取次数']}")

        # 安全相关配置
        logging.info(f"同秒访问检测阈值: {免费账号模块配置['同秒访问检测阈值']} (访问时间记录保留数量等于此值)")
        logging.info(f"设备锁定超时秒数: {免费账号模块配置['设备锁定超时秒数']}")
        logging.info(f"启用IP限制检查: {免费账号模块配置['启用IP限制检查']}")
        logging.info(f"启用设备限制检查: {免费账号模块配置['启用设备限制检查']}")
        logging.info(f"启用IP天数循环限制: {免费账号模块配置['启用IP天数循环限制']} (第一次可用，后续所有天都不能获取)")

        # 缓存相关配置
        logging.info(f"设备信息缓存秒数: {免费账号模块配置['设备信息缓存秒数']}")
        logging.info(f"系统配置缓存秒数: {免费账号模块配置['系统配置缓存秒数']}")
        logging.info(f"缓存清理间隔秒数: {免费账号模块配置['缓存清理间隔秒数']}")

        # 错误处理配置
        logging.info(f"API超时秒数: {免费账号模块配置['API超时秒数']}")
        logging.info(f"数据库连接超时秒数: {免费账号模块配置['数据库连接超时秒数']}")

        # 业务逻辑配置
        logging.info(f"邮箱后缀: {免费账号模块配置['邮箱后缀']}")
        logging.info(f"删除原因_正常使用: {免费账号模块配置['删除原因_正常使用']}")

        # 恶意防护配置
        logging.info("=== 恶意防护配置 ===")
        logging.info("简化配置：同秒访问检测阈值3次自动永久禁用IP，错误请求1次永久封禁")
        logging.info(f"同秒访问检测阈值: {免费账号模块配置['同秒访问检测阈值']} (来自免费账号模块配置)")
        logging.info("错误请求永久封禁次数: 1次")

        logging.info("=== 配置验证完成 ===")
        return True
    except Exception as e:
        logging.error(f"配置验证失败: {e}", exc_info=True)
        return False

# 主程序入口
if __name__ == '__main__':
    try:
        # 验证配置
        验证配置()

        # 设置服务器端口
        port = int(os.environ.get('PORT', 5269))

        # 启动服务器
        logging.info(f"免费限制服务启动在端口 {port}...")
        app.run(host='0.0.0.0', port=port, debug=False)
    except Exception as e:
        logging.critical(f"服务启动失败: {e}", exc_info=True)


