#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 邮箱监控工具
# 功能：
# 1. 监控邮箱系统表，自动将创建时间超过7天的数据迁移到邮箱免费表
# 2. 监控邮箱免费表，自动删除创建时间超过13天的数据
#
# 使用方法：
#   前台运行:     python3 /opt/邮箱监控.py
#   守护进程:     python3 /opt/邮箱监控.py --daemon
#   查看状态:     python3 /opt/邮箱监控.py --status
#   停止监控:     python3 /opt/邮箱监控.py --stop
#   重启监控:     python3 /opt/邮箱监控.py --restart
#   测试连接:     python3 /opt/邮箱监控.py --test

import mysql.connector
from mysql.connector import Error
import time
import datetime
import logging
import schedule
import threading
from typing import Optional, Tuple, List
import signal
import sys
import os
import argparse
import atexit


# 程序配置
数据库配置 = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'Yuyu6709.',
    'database': 'kami3162',
    'charset': 'utf8mb4',
    'autocommit': False
}

监控配置 = {
    '邮箱系统最大天数': 10,   # 邮箱系统表数据保留天数（改为14天）
    '邮箱免费最大天数': 13,  # 邮箱免费表数据保留天数（超过14天删除）
    '邮箱免费最小数量': 1000,  # 邮箱免费表最小保持数量
    '执行时间': '03:00',  # 每天执行时间（凌晨3点）
    '批处理大小': 5000,
    '日志级别': 'INFO',
    '日志文件': '/邮箱监控.log',
    'PID文件': '/var/run/邮箱监控.pid'
}

表配置 = {
    '源表名': '邮箱系统',
    '目标表名': '邮箱免费',
    '创建时间字段候选': ['创建时间', 'created_at', 'create_time', 'created_time', 'add_time', 'insert_time']
}

安全配置 = {
    '安全模式': False,
    '单次最大迁移数量': 10000,
    '迁移前备份': False
}

class 邮箱系统监控器:
    def __init__(self, daemon_mode=False):
        """初始化监控器"""
        # 从配置文件加载配置
        self.数据库配置 = 数据库配置
        self.邮箱系统最大天数 = 监控配置['邮箱系统最大天数']
        self.邮箱免费最大天数 = 监控配置['邮箱免费最大天数']
        self.邮箱免费最小数量 = 监控配置['邮箱免费最小数量']
        self.执行时间 = 监控配置['执行时间']
        self.批处理大小 = 监控配置['批处理大小']
        self.源表名 = 表配置['源表名']
        self.目标表名 = 表配置['目标表名']
        self.创建时间字段候选 = 表配置['创建时间字段候选']
        self.安全模式 = 安全配置['安全模式']
        self.单次最大迁移数量 = 安全配置['单次最大迁移数量']

        # 服务端模式
        self.daemon_mode = daemon_mode
        self.pid_file = 监控配置.get('PID文件', '/var/run/邮箱系统监控.pid')

        # 设置日志
        self._设置日志()

        # 运行状态
        self.运行中 = False
        self.监控线程 = None

        # 统计信息
        self.总迁移数量 = 0
        self.总删除数量 = 0
        self.最后检查时间 = None

        # 设置信号处理
        self._设置信号处理()

    def _设置日志(self):
        """设置日志配置"""
        日志级别 = getattr(logging, 监控配置['日志级别'].upper(), logging.INFO)
        日志文件 = 监控配置['日志文件']

        handlers = [logging.FileHandler(日志文件, encoding='utf-8')]

        # 非守护进程模式下也输出到控制台
        if not self.daemon_mode:
            handlers.append(logging.StreamHandler())

        logging.basicConfig(
            level=日志级别,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=handlers
        )
        self.logger = logging.getLogger(__name__)

    def _设置信号处理(self):
        """设置信号处理"""
        signal.signal(signal.SIGTERM, self._信号处理器)
        signal.signal(signal.SIGINT, self._信号处理器)
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, self._信号处理器)

    def _信号处理器(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备停止监控...")
        self.停止监控()
        sys.exit(0)

    def _创建PID文件(self):
        """创建PID文件"""
        try:
            # 确保PID文件目录存在
            pid_dir = os.path.dirname(self.pid_file)
            if not os.path.exists(pid_dir):
                try:
                    os.makedirs(pid_dir, exist_ok=True)
                except PermissionError:
                    # 如果没有权限，使用当前目录
                    self.pid_file = './邮箱系统监控.pid'

            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))

            # 注册退出时删除PID文件
            atexit.register(self._删除PID文件)
            self.logger.info(f"PID文件已创建: {self.pid_file}")
        except Exception as e:
            self.logger.warning(f"创建PID文件失败: {e}")

    def _删除PID文件(self):
        """删除PID文件"""
        try:
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
                self.logger.info(f"PID文件已删除: {self.pid_file}")
        except Exception as e:
            self.logger.warning(f"删除PID文件失败: {e}")

    def _检查是否已运行(self):
        """检查是否已有实例在运行"""
        if not os.path.exists(self.pid_file):
            return False

        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())

            # 检查进程是否存在
            try:
                os.kill(pid, 0)  # 发送信号0检查进程是否存在
                return True
            except OSError:
                # 进程不存在，删除过期的PID文件
                os.remove(self.pid_file)
                return False
        except (ValueError, FileNotFoundError):
            return False

    def 获取数据库连接(self) -> Optional[mysql.connector.MySQLConnection]:
        """获取数据库连接"""
        try:
            连接 = mysql.connector.connect(**self.数据库配置)
            if 连接.is_connected():
                return 连接
        except Error as e:
            self.logger.error(f"数据库连接失败: {e}")
        return None
        
    def 检查表结构(self, 连接: mysql.connector.MySQLConnection) -> Tuple[bool, List[str], str]:
        """检查表结构并获取字段信息"""
        try:
            游标 = 连接.cursor()

            # 检查源表结构
            游标.execute(f"DESCRIBE `{self.源表名}`")
            字段信息 = 游标.fetchall()
            字段名列表 = [字段[0] for 字段 in 字段信息]

            # 查找创建时间字段
            创建时间字段 = None

            # 使用配置中的候选字段
            for 字段 in self.创建时间字段候选:
                if 字段 in 字段名列表:
                    创建时间字段 = 字段
                    break

            if not 创建时间字段:
                # 如果没有找到标准字段名，检查包含time或date的字段
                for 字段 in 字段名列表:
                    if 'time' in 字段.lower() or 'date' in 字段.lower():
                        创建时间字段 = 字段
                        break

            游标.close()

            if not 创建时间字段:
                self.logger.error(f"未找到创建时间字段，请检查'{self.源表名}'表结构")
                self.logger.error(f"可用字段: {', '.join(字段名列表)}")
                return False, [], ""

            return True, 字段名列表, 创建时间字段

        except Error as e:
            self.logger.error(f"检查表结构失败: {e}")
            return False, [], ""
            
    def 查找过期数据(self, 连接: mysql.connector.MySQLConnection, 表名: str, 创建时间字段: str, 天数: int) -> List[tuple]:
        """查找创建时间超过指定天数的数据"""
        try:
            游标 = 连接.cursor()

            # 计算指定天数前的时间
            截止时间 = datetime.datetime.now() - datetime.timedelta(days=天数)

            # 查询过期数据
            查询语句 = f"""
            SELECT * FROM `{表名}`
            WHERE `{创建时间字段}` < %s
            ORDER BY `{创建时间字段}` ASC
            LIMIT %s
            """

            游标.execute(查询语句, (截止时间, self.批处理大小))
            过期数据 = 游标.fetchall()

            if 过期数据:
                self.logger.debug(f"在表'{表名}'中找到 {len(过期数据)} 条超过{天数}天的过期数据")

            游标.close()
            return 过期数据

        except Error as e:
            self.logger.error(f"查找过期数据失败: {e}")
            return []

    def 删除过期数据(self, 连接: mysql.connector.MySQLConnection, 表名: str, 创建时间字段: str, 天数: int) -> int:
        """直接删除超过指定天数的数据"""
        try:
            游标 = 连接.cursor()

            # 计算指定天数前的时间
            截止时间 = datetime.datetime.now() - datetime.timedelta(days=天数)

            # 删除过期数据
            删除语句 = f"""
            DELETE FROM `{表名}`
            WHERE `{创建时间字段}` < %s
            LIMIT %s
            """

            游标.execute(删除语句, (截止时间, self.批处理大小))
            删除数量 = 游标.rowcount

            # 提交事务
            连接.commit()
            游标.close()

            if 删除数量 > 0:
                self.logger.debug(f"从表'{表名}'中删除了 {删除数量} 条超过{天数}天的过期数据")

            return 删除数量

        except Error as e:
            self.logger.error(f"删除过期数据失败: {e}")
            try:
                连接.rollback()
            except:
                pass
            return 0
            
    def 迁移数据批次(self, 连接: mysql.connector.MySQLConnection,
                   数据批次: List[tuple], 字段名列表: List[str]) -> bool:
        """迁移一批数据到目标表"""
        try:
            游标 = 连接.cursor()

            # 安全检查
            if self.安全模式 and len(数据批次) > self.单次最大迁移数量:
                self.logger.warning(f"安全模式：批次数据量 {len(数据批次)} 超过限制 {self.单次最大迁移数量}")
                return False

            # 构建插入语句
            字段字符串 = ", ".join([f"`{字段}`" for 字段 in 字段名列表])
            占位符 = ", ".join(["%s"] * len(字段名列表))
            插入语句 = f"INSERT INTO `{self.目标表名}` ({字段字符串}) VALUES ({占位符})"

            # 插入数据到目标表
            游标.executemany(插入语句, 数据批次)
            插入数量 = 游标.rowcount

            # 删除源表中的数据（假设第一个字段是ID）
            if 字段名列表:
                ID字段 = 字段名列表[0]
                ID列表 = [行[0] for 行 in 数据批次]
                删除占位符 = ", ".join(["%s"] * len(ID列表))
                删除语句 = f"DELETE FROM `{self.源表名}` WHERE `{ID字段}` IN ({删除占位符})"
                游标.execute(删除语句, ID列表)
                删除数量 = 游标.rowcount

            # 提交事务
            连接.commit()
            游标.close()

            self.logger.debug(f"批次迁移成功：插入 {插入数量} 条，删除 {删除数量} 条")
            return True

        except Error as e:
            self.logger.error(f"迁移数据批次失败: {e}")
            try:
                连接.rollback()
            except:
                pass
            return False
            
    def 执行迁移检查(self):
        """执行一次迁移检查"""
        检查开始时间 = datetime.datetime.now()
        self.logger.info(f"开始执行迁移检查... (邮箱系统保留: {self.邮箱系统最大天数}天, 邮箱免费保留: {self.邮箱免费最大天数}天, 邮箱免费最小数量: {self.邮箱免费最小数量:,})")

        连接 = self.获取数据库连接()
        if not 连接:
            return

        try:
            # 检查表结构
            结构正确, 字段名列表, 创建时间字段 = self.检查表结构(连接)
            if not 结构正确:
                return

            self.logger.info(f"使用创建时间字段: {创建时间字段}")

            # 获取当前表状态
            游标 = 连接.cursor()
            游标.execute(f"SELECT COUNT(*) FROM `{self.源表名}`")
            源表总数 = 游标.fetchone()[0]
            游标.execute(f"SELECT COUNT(*) FROM `{self.目标表名}`")
            目标表总数 = 游标.fetchone()[0]
            游标.close()

            self.logger.info(f"当前状态 - {self.源表名}: {源表总数:,} 条, {self.目标表名}: {目标表总数:,} 条")

            # 第一步：检查邮箱免费表数量，如果不足1万则补充
            本次补充数量 = self._处理邮箱免费补充(连接, 字段名列表, 创建时间字段, 目标表总数)

            # 第二步：处理邮箱系统表的过期数据迁移（7天）
            本次迁移数量 = self._处理邮箱系统迁移(连接, 字段名列表, 创建时间字段)

            # 第三步：处理邮箱免费表的过期数据删除（13天）
            本次删除数量 = self._处理邮箱免费删除(连接, 创建时间字段)

            检查耗时 = datetime.datetime.now() - 检查开始时间
            self.最后检查时间 = datetime.datetime.now()

            self.logger.info(f"本次检查完成 - 补充: {本次补充数量} 条, 迁移: {本次迁移数量} 条, 删除: {本次删除数量} 条, 耗时: {检查耗时}")

        finally:
            if 连接.is_connected():
                连接.close()

    def _处理邮箱免费补充(self, 连接, 字段名列表, 创建时间字段, 当前数量) -> int:
        """检查邮箱免费表数量，如果不足1万则从邮箱系统表补充"""
        if 当前数量 >= self.邮箱免费最小数量:
            self.logger.debug(f"邮箱免费表数量充足: {当前数量:,} >= {self.邮箱免费最小数量:,}")
            return 0

        需要补充数量 = self.邮箱免费最小数量 - 当前数量
        self.logger.info(f"邮箱免费表数量不足，需要补充 {需要补充数量:,} 条数据")

        本次补充数量 = 0
        剩余需要补充 = 需要补充数量

        while 剩余需要补充 > 0:
            # 从邮箱系统表获取最新的数据（按创建时间倒序）
            try:
                游标 = 连接.cursor()
                查询语句 = f"""
                SELECT * FROM `{self.源表名}`
                ORDER BY `{创建时间字段}` DESC
                LIMIT %s
                """

                当前批次大小 = min(self.批处理大小, 剩余需要补充)
                游标.execute(查询语句, (当前批次大小,))
                补充数据 = 游标.fetchall()
                游标.close()

                if not 补充数据:
                    self.logger.warning("邮箱系统表中没有更多数据可供补充")
                    break

                # 迁移数据到邮箱免费表
                if self.迁移数据批次(连接, 补充数据, 字段名列表):
                    本次补充数量 += len(补充数据)
                    剩余需要补充 -= len(补充数据)
                    self.logger.info(f"补充 {len(补充数据)} 条数据到邮箱免费表，剩余需要: {剩余需要补充} 条")
                else:
                    self.logger.error("数据补充失败，停止处理")
                    break

                # 如果获取的数据少于批处理大小，说明邮箱系统表数据不足
                if len(补充数据) < 当前批次大小:
                    self.logger.warning("邮箱系统表数据不足，无法完全补充到1万条")
                    break

            except Exception as e:
                self.logger.error(f"补充数据过程中出错: {e}")
                break

        return 本次补充数量

    def _处理邮箱系统迁移(self, 连接, 字段名列表, 创建时间字段) -> int:
        """处理邮箱系统表的过期数据迁移"""
        本次迁移数量 = 0

        while True:
            # 查找邮箱系统表中超过7天的数据
            过期数据 = self.查找过期数据(连接, self.源表名, 创建时间字段, self.邮箱系统最大天数)

            if not 过期数据:
                break

            # 迁移数据到邮箱免费表
            if self.迁移数据批次(连接, 过期数据, 字段名列表):
                本次迁移数量 += len(过期数据)
                self.总迁移数量 += len(过期数据)
                self.logger.info(f"迁移 {len(过期数据)} 条数据从邮箱系统到邮箱免费表，本次累计: {本次迁移数量} 条")
            else:
                self.logger.error("邮箱系统数据迁移失败，停止处理")
                break

            # 如果批次数据少于批处理大小，说明已经处理完所有过期数据
            if len(过期数据) < self.批处理大小:
                break

        return 本次迁移数量

    def _处理邮箱免费删除(self, 连接, 创建时间字段) -> int:
        """处理邮箱免费表的过期数据删除"""
        本次删除数量 = 0

        while True:
            # 直接删除邮箱免费表中超过13天的数据
            删除数量 = self.删除过期数据(连接, self.目标表名, 创建时间字段, self.邮箱免费最大天数)

            if 删除数量 == 0:
                break

            本次删除数量 += 删除数量
            self.总删除数量 += 删除数量
            self.logger.info(f"删除 {删除数量} 条超过{self.邮箱免费最大天数}天的数据从邮箱免费表，本次累计: {本次删除数量} 条")

            # 如果删除数量少于批处理大小，说明已经处理完所有过期数据
            if 删除数量 < self.批处理大小:
                break

        return 本次删除数量
                
    def 启动监控(self):
        """启动持续监控"""
        if self.运行中:
            self.logger.warning("监控已在运行中")
            return

        # 检查是否已有实例在运行
        if self._检查是否已运行():
            self.logger.error("检测到已有监控实例在运行，退出")
            return False

        # 创建PID文件
        self._创建PID文件()

        self.运行中 = True
        self.logger.info(f"启动邮箱系统监控，每天 {self.执行时间} 执行检查")

        # 设置定时任务 - 每天指定时间执行
        schedule.every().day.at(self.执行时间).do(self.执行迁移检查)

        # 立即执行一次检查
        self.执行迁移检查()

        # 启动监控线程
        self.监控线程 = threading.Thread(target=self._监控循环, daemon=True)
        self.监控线程.start()

        return True
        
    def _监控循环(self):
        """监控循环"""
        while self.运行中:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次是否有待执行的任务
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                
    def 停止监控(self):
        """停止监控"""
        if not self.运行中:
            return

        self.运行中 = False
        self.logger.info("正在停止监控...")

        if self.监控线程:
            self.监控线程.join(timeout=5)

        # 删除PID文件
        self._删除PID文件()

        self.logger.info("监控已停止")
        
    def 获取状态(self) -> dict:
        """获取监控状态"""
        return {
            '运行状态': '运行中' if self.运行中 else '已停止',
            '执行时间': f"每天 {self.执行时间}",
            '邮箱系统保留天数': f"{self.邮箱系统最大天数} 天",
            '邮箱免费保留天数': f"{self.邮箱免费最大天数} 天",
            '邮箱免费最小数量': f"{self.邮箱免费最小数量:,} 条",
            '批处理大小': self.批处理大小,
            '源表': self.源表名,
            '目标表': self.目标表名,
            '总迁移数量': self.总迁移数量,
            '总删除数量': self.总删除数量,
            '最后检查时间': self.最后检查时间.strftime('%Y-%m-%d %H:%M:%S') if self.最后检查时间 else '未检查',
            '安全模式': '启用' if self.安全模式 else '禁用'
        }

def 守护进程化():
    """将进程转为守护进程"""
    try:
        # 第一次fork
        pid = os.fork()
        if pid > 0:
            sys.exit(0)  # 父进程退出
    except OSError as e:
        print(f"第一次fork失败: {e}")
        sys.exit(1)

    # 脱离父进程的控制
    os.chdir("/")
    os.setsid()
    os.umask(0)

    try:
        # 第二次fork
        pid = os.fork()
        if pid > 0:
            sys.exit(0)  # 父进程退出
    except OSError as e:
        print(f"第二次fork失败: {e}")
        sys.exit(1)

    # 重定向标准输入输出
    sys.stdout.flush()
    sys.stderr.flush()

    # 关闭标准输入输出
    with open('/dev/null', 'r') as f:
        os.dup2(f.fileno(), sys.stdin.fileno())
    with open('/dev/null', 'w') as f:
        os.dup2(f.fileno(), sys.stdout.fileno())
        os.dup2(f.fileno(), sys.stderr.fileno())

def 解析命令行参数():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='邮箱系统自动迁移监控工具')
    parser.add_argument('-d', '--daemon', action='store_true',
                       help='以守护进程模式运行')
    parser.add_argument('-s', '--stop', action='store_true',
                       help='停止运行中的监控进程')
    parser.add_argument('-r', '--restart', action='store_true',
                       help='重启监控进程')
    parser.add_argument('--status', action='store_true',
                       help='查看监控状态')
    parser.add_argument('--test', action='store_true',
                       help='测试数据库连接')
    return parser.parse_args()

def 停止现有进程():
    """停止现有的监控进程"""
    pid_file = 监控配置.get('PID文件', '/var/run/邮箱系统监控.pid')
    if not os.path.exists(pid_file):
        print("没有找到运行中的监控进程")
        return False

    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        # 发送TERM信号
        os.kill(pid, signal.SIGTERM)

        # 等待进程结束
        for _ in range(10):
            try:
                os.kill(pid, 0)
                time.sleep(1)
            except OSError:
                break
        else:
            # 如果进程仍然存在，发送KILL信号
            try:
                os.kill(pid, signal.SIGKILL)
            except OSError:
                pass

        print(f"监控进程 {pid} 已停止")
        return True

    except (ValueError, FileNotFoundError, OSError) as e:
        print(f"停止进程失败: {e}")
        return False

def 查看状态():
    """查看监控状态"""
    pid_file = 监控配置.get('PID文件', '/var/run/邮箱系统监控.pid')
    if not os.path.exists(pid_file):
        print("监控进程未运行")
        return

    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        # 检查进程是否存在
        try:
            os.kill(pid, 0)
            print(f"监控进程正在运行 (PID: {pid})")

            # 显示日志文件信息
            日志文件 = 监控配置['日志文件']
            if os.path.exists(日志文件):
                stat = os.stat(日志文件)
                修改时间 = datetime.datetime.fromtimestamp(stat.st_mtime)
                print(f"日志文件: {日志文件}")
                print(f"最后修改: {修改时间.strftime('%Y-%m-%d %H:%M:%S')}")

        except OSError:
            print("PID文件存在但进程未运行")
            os.remove(pid_file)

    except (ValueError, FileNotFoundError) as e:
        print(f"读取状态失败: {e}")

def 测试数据库连接():
    """测试数据库连接"""
    print("测试数据库连接...")
    try:
        连接 = mysql.connector.connect(**数据库配置)
        if 连接.is_connected():
            print("✅ 数据库连接成功")

            游标 = 连接.cursor()
            游标.execute("SELECT VERSION()")
            版本 = 游标.fetchone()[0]
            print(f"MySQL版本: {版本}")

            # 检查表是否存在
            for 表名 in [表配置['源表名'], 表配置['目标表名']]:
                游标.execute(f"SHOW TABLES LIKE '{表名}'")
                if 游标.fetchone():
                    游标.execute(f"SELECT COUNT(*) FROM `{表名}`")
                    数量 = 游标.fetchone()[0]
                    print(f"表 '{表名}': {数量:,} 条记录")
                else:
                    print(f"❌ 表 '{表名}' 不存在")

            游标.close()
            连接.close()
            return True
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    args = 解析命令行参数()

    # 处理命令行参数
    if args.stop:
        停止现有进程()
        return

    if args.status:
        查看状态()
        return

    if args.test:
        测试数据库连接()
        return

    if args.restart:
        print("重启监控进程...")
        停止现有进程()
        time.sleep(2)

    # 守护进程模式
    if args.daemon:
        print("启动守护进程模式...")
        守护进程化()

    # 创建监控器
    监控器 = 邮箱系统监控器(daemon_mode=args.daemon)

    try:
        if not args.daemon:
            print("=" * 60)
            print("邮箱系统自动迁移监控工具")
            print("每天凌晨3点自动执行数据迁移和清理")
            print("=" * 60)

            # 显示配置信息
            状态 = 监控器.获取状态()
            print("\n📋 监控配置:")
            for 键, 值 in 状态.items():
                print(f"   {键}: {值}")

            print(f"\n🔍 监控目标:")
            print(f"   数据库: {监控器.数据库配置['host']}:{监控器.数据库配置['port']}")
            print(f"   数据库名: {监控器.数据库配置['database']}")
            print(f"   源表: {监控器.源表名}")
            print(f"   目标表: {监控器.目标表名}")

            print(f"\n⚙️  高级设置:")
            print(f"   安全模式: {'启用' if 监控器.安全模式 else '禁用'}")
            print(f"   日志文件: {监控配置['日志文件']}")
            print(f"   日志级别: {监控配置['日志级别']}")

        # 启动监控
        if 监控器.启动监控():
            if not args.daemon:
                print(f"\n✅ 监控已启动！")
                print(f"💡 按 Ctrl+C 停止监控")

            # 保持程序运行
            while 监控器.运行中:
                time.sleep(1)
        else:
            print("启动监控失败")
            sys.exit(1)

    except KeyboardInterrupt:
        if not args.daemon:
            print(f"\n\n⏹️  收到停止信号...")
        监控器.停止监控()
        if not args.daemon:
            print("👋 监控已停止，程序退出")

    except Exception as e:
        监控器.logger.error(f"程序异常: {e}")
        监控器.停止监控()
        sys.exit(1)

if __name__ == "__main__":
    main()

