<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #4a6baf;
            margin-bottom: 30px;
        }
        .search-box {
            display: flex;
            margin-bottom: 20px;
        }
        input[type="text"] {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 16px;
            outline: none;
        }
        button {
            background-color: #4a6baf;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 0 4px 4px 0;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #3a5a9f;
        }
        .agent-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #4a6baf;
            display: none;
        }
        .agent-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4a6baf;
        }
        .results {
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 10px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #f2f6ff;
            font-weight: 600;
            color: #4a6baf;
        }
        tr:hover {
            background-color: #f9f9f9;
        }
        .error-message {
            color: #e74c3c;
            text-align: center;
            padding: 10px;
            display: none;
        }
        .loading {
            text-align: center;
            display: none;
        }
        .loading:after {
            content: " .";
            animation: dots 1s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { content: " ."; }
            40% { content: " .."; }
            60% { content: " ..."; }
            80%, 100% { content: " ...."; }
        }
        .stats-container {
            margin-top: 20px;
            display: none;
        }
        .time-filters {
            display: flex;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .time-filter-btn {
            background-color: #6c7ae0;
            color: white;
            border: none;
            padding: 8px 15px;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .time-filter-btn:hover {
            background-color: #5563cf;
        }
        .time-filter-btn.active {
            background-color: #3a4db3;
            font-weight: bold;
        }
        .commission-info {
            padding: 15px;
            background-color: #f0f4ff;
            border-radius: 4px;
            border-left: 4px solid #6c7ae0;
            margin-bottom: 20px;
        }
        .commission-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4a6baf;
        }
        .commission-amount {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>代理订单查询系统</h1>
        
        <div class="search-box">
            <input type="text" id="secretKey" placeholder="请输入代理秘钥">
            <button id="searchBtn">查询</button>
        </div>
        
        <div id="loading" class="loading">正在查询中</div>
        <div id="errorMessage" class="error-message"></div>
        
        <div id="agentInfo" class="agent-info">
            <div class="agent-name">代理名称：<span id="agentName"></span></div>
        </div>
        
        <div id="statsContainer" class="stats-container">
            <div class="time-filters">
                <button class="time-filter-btn" data-period="today">今天</button>
                <button class="time-filter-btn" data-period="yesterday">昨天</button>
                <button class="time-filter-btn" data-period="this_week">本周</button>
                <button class="time-filter-btn" data-period="last_week">上周</button>
                <button class="time-filter-btn" data-period="this_month">本月</button>
            </div>
            
            <div class="commission-info">
                <div class="commission-title">分成金额（<span id="commissionRate">50%</span>）：</div>
                <div class="commission-amount" id="commissionAmount">￥0.00</div>
            </div>
        </div>
        
        <div id="results" class="results" style="display:none;">
            <h2>订单列表</h2>
            <table>
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>卡密</th>
                        <th>类型</th>
                        <th>支付时间</th>
                        <th>金额</th>
                    </tr>
                </thead>
                <tbody id="resultList">
                    <!-- 结果将通过JavaScript动态填充 -->
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchBtn = document.getElementById('searchBtn');
            const secretKeyInput = document.getElementById('secretKey');
            const agentInfo = document.getElementById('agentInfo');
            const agentNameSpan = document.getElementById('agentName');
            const results = document.getElementById('results');
            const resultList = document.getElementById('resultList');
            const errorMessage = document.getElementById('errorMessage');
            const loading = document.getElementById('loading');
            const statsContainer = document.getElementById('statsContainer');
            const commissionAmount = document.getElementById('commissionAmount');
            const timeFilterBtns = document.querySelectorAll('.time-filter-btn');
            
            let currentAgentKey = ''; // 当前查询的代理秘钥
            
            // 检查是否有保存的代理秘钥
            const savedKey = localStorage.getItem('agentKey');
            if (savedKey) {
                // 自动填入秘钥
                secretKeyInput.value = savedKey;
                // 自动执行查询
                searchAgent();
            }
            
            // 按钮点击事件
            searchBtn.addEventListener('click', function() {
                searchAgent();
            });
            
            // 输入框回车事件
            secretKeyInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchAgent();
                }
            });
            
            // 时间筛选按钮点击事件
            timeFilterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (!currentAgentKey) return;
                    
                    // 更新活动状态
                    timeFilterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const period = this.getAttribute('data-period');
                    fetchCommissionData(currentAgentKey, period);
                });
            });
            
            // 搜索代理函数
            function searchAgent() {
                const secretKey = secretKeyInput.value.trim();
                
                if (!secretKey) {
                    showError('请输入代理秘钥');
                    return;
                }
                
                // 保存当前查询的秘钥
                currentAgentKey = secretKey;
                
                // 重置显示
                agentInfo.style.display = 'none';
                results.style.display = 'none';
                statsContainer.style.display = 'none';
                errorMessage.style.display = 'none';
                loading.style.display = 'block';
                
                // 发送查询请求
                fetch('/api/agent/check_key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_key: secretKey
                    })
                })
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    
                    if (!data.success) {
                        showError(data.message || '代理秘钥不存在');
                        return;
                    }
                    
                    // 查询成功，保存秘钥到本地存储
                    localStorage.setItem('agentKey', secretKey);
                    
                    // 显示代理信息
                    agentNameSpan.textContent = data.agent_name;
                    
                    // 更新分成比例显示
                    const commissionRate = data.commission_rate * 100; // 转为百分比
                    document.getElementById('commissionRate').textContent = commissionRate.toFixed(0) + '%';
                    
                    // 保存屏蔽年卡设置到全局变量
                    window.blockYearlyCards = data.block_yearly_cards;
                    
                    agentInfo.style.display = 'block';
                    statsContainer.style.display = 'block';
                    
                    // 显示卡密列表
                    if (data.cards && data.cards.length > 0) {
                        displayResults(data.cards);
                    } else {
                        results.style.display = 'none';
                        showError('此代理暂无订单数据');
                    }
                    
                    // 默认查询本月的分成金额
                    document.querySelector('.time-filter-btn[data-period="this_month"]').click();
                })
                .catch(error => {
                    loading.style.display = 'none';
                    showError('查询失败，请稍后重试');
                    console.error('查询错误:', error);
                });
            }
            
            // 获取分成金额数据
            function fetchCommissionData(agentKey, period) {
                loading.style.display = 'block';
                errorMessage.style.display = 'none';
                
                fetch('/api/agent/commission', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_key: agentKey,
                        period: period
                    })
                })
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    
                    if (!data.success) {
                        showError(data.message || '获取分成数据失败');
                        return;
                    }
                    
                    // 显示分成金额
                    commissionAmount.textContent = "￥" + parseFloat(data.commission_amount).toFixed(2);
                    
                    // 更新分成比例显示（如果有变化）
                    if (data.commission_rate) {
                        const commissionRate = data.commission_rate * 100; // 转为百分比
                        document.getElementById('commissionRate').textContent = commissionRate.toFixed(0) + '%';
                    }
                })
                .catch(error => {
                    loading.style.display = 'none';
                    showError('获取分成数据失败，请稍后重试');
                    console.error('获取分成数据错误:', error);
                });
            }
            
            // 展示错误信息
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
            }
            
            // 显示查询结果
            function displayResults(cards) {
                resultList.innerHTML = '';
                
                // 获取是否屏蔽年卡的设置
                const blockYearlyCards = window.blockYearlyCards || 0;
                
                // 根据屏蔽年卡设置决定是否过滤
                let displayCards = cards;
                if (blockYearlyCards === 1) {
                    // 过滤掉年卡相关的卡密
                    displayCards = cards.filter(card => {
                        const cardType = card.类型 || '';
                        return !cardType.includes('年卡') && !cardType.includes('续卡-年卡');
                    });
                }
                
                if (displayCards.length === 0) {
                    results.style.display = 'none';
                    showError('此代理暂无可显示的订单数据');
                    return;
                }
                
                displayCards.forEach(card => {
                    const row = document.createElement('tr');
                    
                    // 创建并添加订单号单元格
                    const orderCell = document.createElement('td');
                    orderCell.textContent = card.订单号;
                    row.appendChild(orderCell);
                    
                    // 创建并添加卡密单元格
                    const keyCell = document.createElement('td');
                    keyCell.textContent = card.卡密;
                    row.appendChild(keyCell);
                    
                    // 创建并添加类型单元格
                    const typeCell = document.createElement('td');
                    typeCell.textContent = card.类型;
                    row.appendChild(typeCell);
                    
                    // 创建并添加支付时间单元格，格式化为"YYYY-MM-DD HH:MM:SS"
                    const payTimeCell = document.createElement('td');
                    if (card.支付时间) {
                        // 直接解析数据库返回的时间字符串，避免时区转换
                        const dbTime = parseDbDateTime(card.支付时间);
                        payTimeCell.textContent = dbTime;
                    } else {
                        payTimeCell.textContent = '未支付';
                    }
                    row.appendChild(payTimeCell);
                    
                    // 创建并添加金额单元格
                    const amountCell = document.createElement('td');
                    amountCell.textContent = "￥" + parseFloat(card.金额).toFixed(2);
                    row.appendChild(amountCell);
                    
                    resultList.appendChild(row);
                });
                
                results.style.display = 'block';
            }
            
            // 直接从数据库时间字符串解析为格式化的时间，不进行时区转换
            function parseDbDateTime(dbTimeStr) {
                // 处理不同格式的时间字符串
                let timeStr = dbTimeStr;
                
                // 月份名称映射
                const monthMap = {
                    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
                    'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
                };
                
                // 处理格式如 "Fri, 09 May 2025 15:20:35 GMT"
                if (timeStr.includes('GMT')) {
                    const parts = timeStr.split(' ');
                    if (parts.length >= 5) {
                        const day = parts[1].padStart(2, '0');
                        const month = monthMap[parts[2]] || '01';
                        const year = parts[3];
                        const timeParts = parts[4].split(':');
                        if (timeParts.length >= 3) {
                            const hour = timeParts[0].padStart(2, '0');
                            const minute = timeParts[1].padStart(2, '0');
                            const second = timeParts[2].padStart(2, '0');
                            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
                        }
                    }
                }
                
                // 如果是ISO格式
                if (timeStr.includes('T')) {
                    const match = timeStr.match(/(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})/);
                    if (match) {
                        const [_, year, month, day, hour, minute, second] = match;
                        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
                    }
                }
                
                // 尝试匹配常规的日期格式 "YYYY-MM-DD HH:MM:SS"
                const dateMatch = timeStr.match(/(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/);
                if (dateMatch) {
                    const [_, year, month, day, hour, minute, second] = dateMatch;
                    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
                }
                
                // 如果是简单的格式但可能有额外字符，截取标准部分
                if (timeStr.length > 19 && timeStr.match(/^\d{4}-\d{2}-\d{2}/)) {
                    return timeStr.substring(0, 19);
                }
                
                // 如果无法解析，返回原始字符串
                return timeStr;
            }
        });
    </script>
</body>
</html>
