<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量卡密购买系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 30px;
        }
        h1 {
            text-align: center;
            color: #3f51b5;
            margin-bottom: 30px;
        }
        .header-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .card-types {
            margin-top: 30px;
        }
        .card-type-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #3f51b5;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .card-list {
            display: flex;
            flex-wrap: nowrap;
            gap: 25px;
        }
        .card-item {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            flex: 1;
            min-width: 200px;
            box-sizing: border-box;
            transition: transform 0.3s, box-shadow 0.3s;
            position: relative;
            background-color: #fff;
        }
        .card-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }
        .card-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .card-price {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }
        .original-price {
            color: #999;
            text-decoration: line-through;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .discount-price {
            color: #f44336;
            font-size: 20px;
            font-weight: bold;
        }
        .discount-badge {
            position: absolute;
            top: -12px;
            right: -12px;
            background-color: #ff9800;
            color: white;
            font-size: 16px;
            font-weight: bold;
            padding: 6px 12px;
            border-radius: 20px;
            box-shadow: 0 3px 5px rgba(0,0,0,0.2);
        }
        .buy-btn {
            width: 100%;
            padding: 12px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: bold;
        }
        .buy-btn:hover {
            background-color: #388E3C;
        }
        .quantity-control {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .quantity-btn {
            width: 35px;
            height: 35px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            user-select: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .quantity-btn:hover {
            background-color: #e0e0e0;
        }
        .quantity-input {
            width: 50px;
            height: 35px;
            text-align: center;
            border: 1px solid #ddd;
            margin: 0 8px;
            font-size: 16px;
            border-radius: 4px;
        }
        .min-quantity-notice {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            background-color: #f5f5f5;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #ffc107;
        }
        .error-message {
            color: #e74c3c;
            text-align: center;
            padding: 12px;
            display: none;
            background-color: #fdeaea;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .info-message {
            color: #3498db;
            text-align: center;
            padding: 12px;
            display: none;
            background-color: #e8f4fc;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .loading {
            text-align: center;
            display: none;
            padding: 20px;
        }
        .loading:after {
            content: " .";
            animation: dots 1s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { content: " ."; }
            40% { content: " .."; }
            60% { content: " ..."; }
            80%, 100% { content: " ...."; }
        }
        .order-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            display: none;
        }
        .order-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 5px 25px rgba(0,0,0,0.2);
        }
        .order-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 25px;
            text-align: center;
            color: #3f51b5;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        .order-info {
            margin-bottom: 25px;
        }
        .order-row {
            display: flex;
            margin-bottom: 15px;
        }
        .order-label {
            width: 100px;
            color: #666;
            font-size: 16px;
        }
        .order-value {
            flex: 1;
            font-weight: bold;
            font-size: 16px;
        }
        .order-action {
            text-align: center;
            margin-top: 25px;
        }
        .pay-btn {
            padding: 12px 35px;
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: bold;
        }
        .pay-btn:hover {
            background-color: #d32f2f;
        }
        .close-btn {
            padding: 12px 35px;
            background-color: #9e9e9e;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin-left: 15px;
            transition: background-color 0.3s;
        }
        .close-btn:hover {
            background-color: #757575;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .card-item {
                width: calc(50% - 25px);
            }
        }
        
        @media (max-width: 576px) {
            .card-item {
                width: 100%;
            }
        }
        
        .discount-hint-container {
            margin-top: 10px;
            font-size: 13px;
            color: #ff6d00;
            background-color: #fff8e1;
            border-radius: 4px;
            padding: 5px 8px;
            display: none;  /* 默认隐藏 */
            border-left: 3px solid #ff9800;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }

        .download-container {
            max-width: 1000px;
            margin: 0 auto 30px auto;
            background-color: #e8f5e9;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }

        .download-title {
            font-size: 18px;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .download-desc {
            font-size: 14px;
            color: #555;
            margin-bottom: 15px;
        }

        .download-button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .download-button:hover {
            background-color: #388E3C;
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
        }

        .download-icon {
            display: inline-block;
            font-style: normal;
            margin-right: 5px;
            animation: bounce 1s infinite alternate;
        }

        @keyframes bounce {
            0% { transform: translateY(0); }
            100% { transform: translateY(-3px); }
        }

        .discount-info {
            text-align: center;
            margin: 0 auto 20px auto;
            font-size: 14px;
            max-width: 1000px;
        }

        .discount-info details {
            display: inline-block;
            cursor: pointer;
        }

        .discount-info summary {
            color: #3f51b5;
            font-weight: bold;
            outline: none;
        }

        .discount-hint {
            color: #999;
            font-size: 12px;
            font-weight: normal;
        }

        .discount-table {
            margin-top: 10px;
            display: inline-block;
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .discount-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            min-width: 120px;
        }

        .discount-row span {
            margin: 0 15px;
        }

        .discount-row span:last-child {
            font-weight: bold;
            color: #f44336;
        }

        .additional-info {
            max-width: 1000px;
            margin: 20px auto;
            background-color: #e3f2fd;
            border-radius: 8px;
            padding: 15px 20px;
            border-left: 4px solid #2196f3;
            color: #1565c0;
            font-size: 15px;
        }

        .additional-info p {
            margin-top: 0;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .additional-info ul {
            margin: 0;
            padding-left: 20px;
        }

        .additional-info li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>分销批量购卡系统</h1>
        
        <div class="discount-info">
            <details>
                <summary>批量购买折扣说明 <span class="discount-hint">点击查看</span></summary>
                <div class="discount-table">
                    <div class="discount-row"><span>5-9张</span><span>8折</span></div>
                    <div class="discount-row"><span>10-49张</span><span>7折</span></div>
                    <div class="discount-row"><span>50-99张</span><span>6折</span></div>
                    <div class="discount-row"><span>100张以上</span><span>5折</span></div>
                </div>
            </details>
        </div>
        
        <div class="additional-info">
            <p><strong>说明：</strong> 购买的卡密，未激活不计时长</p>
        </div>
        
        <div id="loading" class="loading">正在加载中</div>
        <div id="errorMessage" class="error-message"></div>
        
        <div id="cardTypes" class="card-types">
            <div class="card-type-title">卡密类型</div>
            <div id="cardList" class="card-list">
                <!-- 卡密类型列表将通过JavaScript动态填充 -->
            </div>
        </div>
    </div>
    
    <div id="orderModal" class="order-modal">
        <div class="order-content">
            <div class="order-title">确认订单</div>
            <div class="order-info">
                <div class="order-row">
                    <div class="order-label">卡密类型：</div>
                    <div id="modalCardType" class="order-value"></div>
                </div>
                <div class="order-row">
                    <div class="order-label">数量：</div>
                    <div id="modalQuantity" class="order-value"></div>
                </div>
                <div class="order-row">
                    <div class="order-label">原价：</div>
                    <div id="modalOriginalPrice" class="order-value"></div>
                </div>
                <div class="order-row">
                    <div class="order-label">折扣价：</div>
                    <div id="modalDiscountPrice" class="order-value"></div>
                </div>
                <div class="order-row">
                    <div class="order-label">总价：</div>
                    <div id="modalTotalPrice" class="order-value"></div>
                </div>
            </div>
            <div class="order-action">
                <button id="payBtn" class="pay-btn">确认支付</button>
                <button id="closeBtn" class="close-btn">取消</button>
            </div>
        </div>
    </div>
    
    <div id="downloadModal" class="order-modal">
        <div class="order-content">
            <div class="order-title">支付成功</div>
            <div class="order-info">
                <p>您已成功购买批量卡密，请点击下方按钮下载卡密文件。</p>
                <p>注意：请妥善保管您的卡密信息，卡密未激活前不计时。</p>
            </div>
            <div class="order-action">
                <button id="downloadBtn" class="pay-btn">下载卡密</button>
                <button id="closeDownloadBtn" class="close-btn">关闭</button>
            </div>
        </div>
    </div>
    
    <div class="download-container">
        <div class="download-title">无内购纯净版分销包下载</div>
        <a href="https://www.123684.com/s/ukekTd-Smml" class="download-button" target="_blank">
            <i class="download-icon">↓</i> 下载安装包
        </a>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const cardList = document.getElementById('cardList');
            const errorMessage = document.getElementById('errorMessage');
            const loading = document.getElementById('loading');
            const orderModal = document.getElementById('orderModal');
            const modalCardType = document.getElementById('modalCardType');
            const modalQuantity = document.getElementById('modalQuantity');
            const modalOriginalPrice = document.getElementById('modalOriginalPrice');
            const modalDiscountPrice = document.getElementById('modalDiscountPrice');
            const modalTotalPrice = document.getElementById('modalTotalPrice');
            const payBtn = document.getElementById('payBtn');
            const closeBtn = document.getElementById('closeBtn');
            
            let currentCardData = null; // 当前选择的卡密数据
            let currentQuantity = 1; // 当前选择的数量，默认为1
            let currentDiscount = 1.0; // 当前折扣率
            
            // 初始化页面
            loading.style.display = 'block';
            loadCardTypes();
            
            // 关闭订单模态框
            closeBtn.addEventListener('click', function() {
                orderModal.style.display = 'none';
            });
            
            // 点击支付按钮
            payBtn.addEventListener('click', function() {
                // 保存订单信息，准备在新窗口中打开支付链接
                createOrder();
            });
            
            // 关闭下载模态框
            const downloadModal = document.getElementById('downloadModal');
            const closeDownloadBtn = document.getElementById('closeDownloadBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            
            // 设置下载卡密文件按钮事件
            downloadBtn.addEventListener('click', function() {
                // 如果存在下载链接，跳转到下载页面
                if (window.currentDownloadLink) {
                    window.open(window.currentDownloadLink, '_blank');
                    downloadModal.style.display = 'none';
                }
            });
            
            closeDownloadBtn.addEventListener('click', function() {
                downloadModal.style.display = 'none';
            });
            
            // 检查URL参数，看是否有支付成功的消息
            function checkForPaymentSuccess() {
                const urlParams = new URLSearchParams(window.location.search);
                const orderNo = urlParams.get('order_no');
                const success = urlParams.get('success');
                
                if (orderNo && success === 'true') {
                    // 清除URL参数，防止刷新页面重复显示
                    const newUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);
                    
                    // 显示下载模态框
                    const cardType = urlParams.get('card_type') || '';
                    const quantity = urlParams.get('quantity') || '1';
                    window.currentDownloadLink = `/api/agent/buy/download?order_no=${orderNo}&card_type=${cardType}&quantity=${quantity}`;
                    downloadModal.style.display = 'flex';
                }
            }
            
            // 页面加载时检查
            checkForPaymentSuccess();
            
            // 加载卡密类型
            function loadCardTypes() {
                // 显示加载中
                loading.style.display = 'block';
                
                // 直接从API获取卡密类型列表
                fetch('/api/agent/buy/info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_key: "default" // 使用默认值，后端将忽略验证
                    })
                })
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    
                    if (!data.success) {
                        // 如果API请求失败，使用默认卡密类型
                        console.error('获取卡密类型失败，使用默认卡密类型:', data.message);
                        useDefaultCardTypes();
                        return;
                    }
                    
                    // 显示卡密类型
                    displayCardTypes(data.card_types);
                })
                .catch(error => {
                    loading.style.display = 'none';
                    console.error('获取卡密类型错误，使用默认卡密类型:', error);
                    useDefaultCardTypes();
                });
            }
            
            // 使用默认硬编码的卡密类型
            function useDefaultCardTypes() {
                const defaultCardTypes = [
                    { type: '天卡', price: 6.6 },
                    { type: '周卡', price: 26.9 },
                    { type: '月卡', price: 49.9 },
                    { type: '年卡', price: 360.0 }
                ];
                displayCardTypes(defaultCardTypes);
            }
            
            // 计算折扣
            function calculateDiscount(quantity) {
                if (quantity < 5) {
                    return 1.0; // 5张以下原价
                } else if (quantity <= 9) {
                    return 0.8; // 5-9张：8折
                } else if (quantity <= 49) {
                    return 0.7; // 10-49张：7折
                } else if (quantity <= 99) {
                    return 0.6; // 50-99张：6折
                } else {
                    return 0.5; // 100张以上：5折
                }
            }
            
            // 显示卡密类型
            function displayCardTypes(cardTypes) {
                cardList.innerHTML = '';
                
                if (!cardTypes || cardTypes.length === 0) {
                    cardList.innerHTML = '<div class="error-message" style="display:block">未找到可用的卡密类型，请联系管理员</div>';
                    return;
                }
                
                cardTypes.forEach(card => {
                    const originalPrice = card.price;
                    // 默认不显示折扣
                    const discountPrice = originalPrice;
                    
                    const cardElement = document.createElement('div');
                    cardElement.className = 'card-item';
                    
                    cardElement.innerHTML = `
                        <div class="card-name">${card.type}</div>
                        <div class="card-price">
                            <div class="discount-price">￥${discountPrice.toFixed(2)}</div>
                        </div>
                        <div class="quantity-control">
                            <div class="quantity-btn" data-action="decrease">-</div>
                            <input type="number" class="quantity-input" value="1">
                            <div class="quantity-btn" data-action="increase">+</div>
                        </div>
                        <div class="discount-hint-container"></div>
                        <button class="buy-btn">立即购买</button>
                    `;
                    
                    // 获取数量控制元素
                    const quantityInput = cardElement.querySelector('.quantity-input');
                    const decreaseBtn = cardElement.querySelector('[data-action="decrease"]');
                    const increaseBtn = cardElement.querySelector('[data-action="increase"]');
                    
                    // 数量减少按钮点击事件
                    decreaseBtn.addEventListener('click', function() {
                        let value = parseInt(quantityInput.value);
                        
                        // 确保value是整数
                        if (isNaN(value)) {
                            value = 1;
                        }
                        
                        let newValue = value - 5; // 尝试减少5张

                        if (value >= 5) {
                             // 如果当前数量大于等于5，减少到小于等于当前数量的最近的5的倍数
                            newValue = Math.max(5, Math.floor((value - 1) / 5) * 5);
                        } else if (value > 1) {
                            // 如果是2-4，减少1
                            newValue = value - 1;
                        } else {
                            // 如果是1，保持1
                            newValue = 1;
                        }
                        
                        quantityInput.value = newValue;
                        updateDiscount(card, cardElement);
                    });
                    
                    // 数量增加按钮点击事件
                    increaseBtn.addEventListener('click', function() {
                        let value = parseInt(quantityInput.value);
                        
                        // 确保value是整数
                        if (isNaN(value)) {
                            value = 1;
                        }
                        
                        let newValue;
                        if (value < 5) {
                            // 如果小于5，直接到5
                            newValue = 5;
                        } else {
                            // 如果大于等于5，跳转到下一个5的倍数
                            // 计算当前值距离下一个5的倍数还差多少
                            const remainder = value % 5;
                            if (remainder === 0) {
                                // 如果已经是5的倍数，直接加5
                                newValue = value + 5;
                            } else {
                                // 如果不是5的倍数，加到下一个5的倍数
                                newValue = value + (5 - remainder);
                            }
                        }
                        
                        // 确保不超过最大限制 (100)
                        if (newValue > 100) {
                           newValue = 100;
                        }
                        
                        quantityInput.value = newValue; // 确保更新输入框的值
                        updateDiscount(card, cardElement);
                    });
                    
                    // 输入框输入事件，确保数量不小于最低要求并处理5的倍数
                    quantityInput.addEventListener('change', function() {
                        let value = parseInt(this.value);
                        
                        // 确保值是数字且不小于1
                        if (isNaN(value) || value < 1) {
                            value = 1;
                        }
                        
                        // 确保不超过100
                        if (value > 100) {
                            value = 100;
                        }
                        
                        // 更新输入框的值为整数
                        this.value = value;
                        
                        updateDiscount(card, cardElement);
                    });
                    
                    // 同时监听input事件，确保实时更新折扣
                    quantityInput.addEventListener('input', function() {
                        // 当输入框值变化时实时更新折扣
                        let value = parseInt(this.value);
                        // 检查输入是否为有效数字
                        if (isNaN(value)) {
                            // 非数字输入，不执行更新，等待change事件处理
                            return;
                        }
                        
                        // 确保在有效范围内
                        if (value >= 1 && value <= 100) {
                            // 确保输入为整数
                            this.value = value;
                            updateDiscount(card, cardElement);
                        }
                    });
                    
                    // 购买按钮点击事件
                    const buyBtn = cardElement.querySelector('.buy-btn');
                    buyBtn.addEventListener('click', function() {
                        const quantity = parseInt(quantityInput.value);
                        showOrderModal(card, quantity);
                    });
                    
                    // 初始化折扣显示
                    updateDiscount(card, cardElement);
                    
                    cardList.appendChild(cardElement);
                });
                
                document.getElementById('cardTypes').style.display = 'block';
            }
            
            // 更新折扣显示
            function updateDiscount(card, cardElement) {
                const quantityInput = cardElement.querySelector('.quantity-input');
                const quantity = parseInt(quantityInput.value);
                const discount = calculateDiscount(quantity);
                const originalPrice = card.price;
                const discountPrice = (originalPrice * discount).toFixed(2);
                
                const priceDiv = cardElement.querySelector('.card-price');
                const discountBadge = cardElement.querySelector('.discount-badge');
                const discountHintContainer = cardElement.querySelector('.discount-hint-container');
                
                // 移除旧的折扣标签（如果有）
                if (discountBadge) {
                    discountBadge.remove();
                }
                
                // 更新价格显示
                if (discount < 1) {
                    // 有折扣时显示原价和折扣价
                    let discountText = "";
                    
                    // 将折扣转换为整数值，避免浮点数精度问题
                    const discountPercent = Math.round(discount * 10);
                    
                    // 将数字折扣转换为中文大写
                    const chineseNumerals = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
                    if (discountPercent >= 0 && discountPercent <= 10) {
                        discountText = chineseNumerals[discountPercent] + "折";
                    } else {
                        discountText = discountPercent + "折"; // 处理 unexpected case
                    }
                    
                    // 添加折扣标签
                    const newBadge = document.createElement('div');
                    newBadge.className = 'discount-badge';
                    newBadge.textContent = "当前" + discountText; // 在折扣文本前添加"当前"
                    cardElement.appendChild(newBadge);
                    
                    // 更新价格显示，包含原价
                    priceDiv.innerHTML = `
                        <div class="original-price">原价：￥${originalPrice.toFixed(2)}</div>
                        <div class="discount-price">折后价：￥${discountPrice}</div>
                    `;
                } else {
                    // 无折扣时只显示原价
                    priceDiv.innerHTML = `
                        <div class="discount-price">价格：￥${discountPrice}</div>
                    `;
                }
                
                // 更新折扣提示
                updateDiscountHint(quantity, discountHintContainer);
            }
            
            // 更新折扣提示信息
            function updateDiscountHint(quantity, hintContainer) {
                if (quantity >= 100) { // 如果已经是最高折扣或超过100张，不显示提示
                    // 不显示提示，因为不足5张无法购买 或 已是最高折扣
                    hintContainer.style.display = 'none';
                    return;
                }
                
                let nextDiscountThreshold;
                let nextDiscountRate = 0;
                
                if (quantity < 5) { // 5张以下原价，下一档是5张享8折
                    nextDiscountThreshold = 5;
                    nextDiscountRate = 8;
                } else if (quantity < 10) {
                    nextDiscountThreshold = 10;
                    nextDiscountRate = 7;
                } else if (quantity < 50) {
                    nextDiscountThreshold = 50;
                    nextDiscountRate = 6;
                } else if (quantity < 100) {
                    nextDiscountThreshold = 100;
                    nextDiscountRate = 5;
                }
                
                // 计算还差多少张到下一档
                const remaining = nextDiscountThreshold - quantity;
                
                if (remaining > 0) {
                    hintContainer.innerHTML = `再购买 <strong>${remaining}</strong> 张可享 <strong>${nextDiscountRate}折</strong> 优惠！`;
                    hintContainer.style.display = 'block';
                } else {
                    hintContainer.style.display = 'none';
                }
            }
            
            // 显示订单确认模态框
            function showOrderModal(card, quantity) {
                currentCardData = card;
                currentQuantity = quantity;
                
                const originalPrice = card.price;
                const discount = calculateDiscount(quantity);
                const discountPrice = (originalPrice * discount).toFixed(2);
                const totalPrice = (discountPrice * quantity).toFixed(2);
                
                modalCardType.textContent = card.type;
                modalQuantity.textContent = quantity + " 张";
                modalOriginalPrice.textContent = `￥${originalPrice.toFixed(2)}`;
                
                if (discount < 1) {
                    // 将折扣转换为整数值，避免浮点数精度问题
                    const discountPercent = Math.round(discount * 10);
                    const discountText = discountPercent + "折";
                    
                    modalDiscountPrice.textContent = `￥${discountPrice} (${discountText})`;
                } else {
                    modalDiscountPrice.textContent = `￥${discountPrice} (无折扣)`;
                }
                
                modalTotalPrice.textContent = `￥${totalPrice}`;
                
                orderModal.style.display = 'flex';
            }
            
            // 创建订单
            function createOrder() {
                if (!currentCardData || !currentQuantity) {
                    return;
                }
                
                loading.style.display = 'block';
                
                fetch('/api/agent/buy/create_order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_key: "default", // 使用默认值，后端将使用默认代理
                        card_type: currentCardData.type,
                        quantity: currentQuantity
                    })
                })
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    
                    if (!data.success) {
                        showError(data.message || '创建订单失败');
                        orderModal.style.display = 'none'; // 关闭订单窗口，显示错误信息
                        return;
                    }
                    
                    // 关闭订单窗口
                    orderModal.style.display = 'none';
                    
                    // 保存订单信息，用于支付完成后处理
                    window.pendingOrderInfo = {
                        orderNo: data.订单号,
                        cardType: currentCardData.type,
                        quantity: currentQuantity
                    };
                    
                    // 在新窗口打开支付链接
                    if (data.支付链接) {
                        window.open(data.支付链接, '_blank');
                        // 显示提示信息
                        showMessage('订单已创建，请在新打开的窗口完成支付。支付完成后请回到此页面下载卡密文件。');
                        
                        // 开始轮询订单状态
                        pollOrderStatus(data.订单号, currentCardData.type, currentQuantity);
                    }
                })
                .catch(error => {
                    loading.style.display = 'none';
                    showError('创建订单失败，请稍后重试');
                    console.error('创建订单错误:', error);
                    orderModal.style.display = 'none'; // 关闭订单窗口，显示错误信息
                });
            }
            
            // 展示错误信息
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
            }
            
            // 添加显示普通消息的函数
            function showMessage(message) {
                // 先隐藏错误样式
                errorMessage.classList.remove('error-message');
                errorMessage.classList.add('info-message');
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                
                // 10秒后自动隐藏
                setTimeout(function() {
                    errorMessage.style.display = 'none';
                }, 10000);
            }
            
            // 轮询检查订单支付状态
            function pollOrderStatus(orderNo, cardType, quantity) {
                if (!orderNo) return;
                
                // 如果已经存在轮询任务，先清除
                if (window.pollInterval) {
                    clearInterval(window.pollInterval);
                }
                
                // 设置轮询间隔（5秒）
                window.pollInterval = setInterval(function() {
                    fetch(`/api/agent/buy/check_order?order_no=${orderNo}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.paid === true) {
                                // 支付成功，清除轮询
                                clearInterval(window.pollInterval);
                                
                                // 设置下载链接
                                window.currentDownloadLink = `/api/agent/buy/download?order_no=${orderNo}&card_type=${cardType}&quantity=${quantity}`;
                                
                                // 显示下载模态框
                                downloadModal.style.display = 'flex';
                                
                                // 显示成功消息
                                showMessage('支付成功！您可以下载卡密文件了。');
                            }
                        })
                        .catch(error => {
                            console.error('轮询订单状态出错:', error);
                        });
                }, 5000);
                
                // 最多轮询5分钟（60 * 5 = 300秒）
                setTimeout(function() {
                    if (window.pollInterval) {
                        clearInterval(window.pollInterval);
                        console.log('轮询订单状态超时，已停止');
                    }
                }, 300000);
            }
        });
    </script>
</body>
</html>
