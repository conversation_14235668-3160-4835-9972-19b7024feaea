#此脚本在服务端运行，用于检查限制
import mysql.connector
import datetime
import logging
import time
import functools
import os
from logging.handlers import RotatingFileHandler
from flask import Flask, request, jsonify
from mysql.connector import pooling  # 添加连接池模块导入

# 配置日志
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
# 使用RotatingFileHandler进行日志轮转
file_handler = RotatingFileHandler(
    os.path.join(log_dir, 'app.log'), 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
console_handler = logging.StreamHandler()

# 生产环境中使用INFO级别，减少日志量
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[file_handler, console_handler]
)

class Config:
    """系统配置类"""
    # 数据库配置
    数据库配置 = {
        'host': 'localhost',
        # 'host': '***************',
        'port': 3306,
        'user': 'root',
        'password': 'YU6709',
        'database': 'kami3162'
    }
    
    # 连接池配置
    连接池配置 = {
        'pool_name': 'mysql_pool',
        'pool_size': 32,  # 连接池大小，MySQL Connector限制最大为32
        'pool_reset_session': True,  # 重置会话状态
        'autocommit': True,  # 自动提交事务
    }
    
    # 连接有效性检查配置
    连接配置 = {
        'connection_timeout': 10,  # 连接超时时间(秒)
        'use_pure': True,  # 使用纯Python实现
        'get_warnings': True,  # 获取警告信息
    }
    
    # 查询超时配置
    查询超时 = 5  # 秒
    
    # 缓存配置
    启用缓存 = True
    缓存过期时间 = 300  # 秒

    # IP限制配置
    IP限制 = {
        '每IP最大设备数': 1,  # 每个IP允许激活的最大设备数量
        '启用IP限制': True,   # 是否启用IP限制功能
    }

    # 试用配置
    试用配置 = {
        '最大获取次数': 3,  # 最大试用次数
        '获取频率限制': 60,  # 获取频率限制（秒），默认60秒
    }
    
    # 付费账号配置
    付费账号配置 = {
        '十分钟最大调用次数': 3,  # 每10分钟最大调用次数
        '二十分钟最大调用次数': 6,  # 每20分钟最大调用次数
        '默认最大机器码数量': 1,  # 默认最大机器码数量
        '获取频率限制': 3,  # 获取频率限制（秒）
    }
    
    # 邮箱账号配置
    邮箱账号配置 = {
        '免费账号最大人数': 1,  # 免费账号最大使用人数
        '付费账号最大人数': 1,  # 付费账号最大使用人数
    }
    
    # 日志配置
    日志配置 = {
        '日志级别': 'INFO',  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
        '日志文件大小': 10*1024*1024,  # 日志文件大小上限（10MB）
        '日志文件数量': 5,  # 日志文件保留数量
    }
    
    # API服务配置
    API服务配置 = {
        '监听地址': '0.0.0.0',  # 监听地址，0.0.0.0表示所有地址
        '端口': 5000,  # 监听端口
        '调试模式': False,  # 是否开启调试模式
    }

# 简单的内存缓存实现
class 缓存管理器:
    """缓存管理器，用于缓存查询结果"""
    def __init__(self):
        self.缓存 = {}
        self.过期时间 = {}
    
    def 获取(self, 键):
        """获取缓存"""
        if not Config.启用缓存:
            return None
        
        当前时间 = time.time()
        if 键 in self.缓存 and 当前时间 < self.过期时间.get(键, 0):
            logging.debug(f"缓存命中: {键}")
            return self.缓存[键]
        
        if 键 in self.缓存:
            # 缓存过期，清除
            del self.缓存[键]
            if 键 in self.过期时间:
                del self.过期时间[键]
        
        return None
    
    def 设置(self, 键, 值, 过期秒数=None):
        """设置缓存"""
        if not Config.启用缓存:
            return
        
        过期秒数 = 过期秒数 or Config.缓存过期时间
        self.缓存[键] = 值
        self.过期时间[键] = time.time() + 过期秒数
        logging.debug(f"缓存设置: {键}, 过期时间: {过期秒数}秒")
    
    def 清除(self, 键=None):
        """清除缓存"""
        if 键 is None:
            self.缓存.clear()
            self.过期时间.clear()
            logging.debug("清除所有缓存")
        elif 键 in self.缓存:
            del self.缓存[键]
            if 键 in self.过期时间:
                del self.过期时间[键]
            logging.debug(f"清除缓存: {键}")
    
    def 清理过期缓存(self):
        """清理所有过期缓存"""
        当前时间 = time.time()
        过期键列表 = [键 for 键, 过期时间 in self.过期时间.items() if 当前时间 >= 过期时间]
        
        for 键 in 过期键列表:
            del self.缓存[键]
            del self.过期时间[键]
        
        if 过期键列表:
            logging.debug(f"清理过期缓存: {len(过期键列表)}项")

# 全局缓存实例
缓存 = 缓存管理器()

class 用户信息:
    """用户信息类"""
    def __init__(self, 机器码="", IP=""):
        self.机器码 = 机器码
        self.IP = IP
        self.免费已试用次数 = 0

class 连接池管理器:
    """数据库连接池管理"""
    _连接池实例 = None
    
    def __init__(self, 配置=None):
        self.数据库配置 = 配置 or Config.数据库配置
        self.池配置 = Config.连接池配置
        self.连接配置 = Config.连接配置
        
        # 合并数据库配置和连接配置
        self.完整配置 = {**self.数据库配置, **self.连接配置}
        
        # 单独处理池配置
        self.池配置字典 = {k: v for k, v in self.池配置.items()}
        
        if 连接池管理器._连接池实例 is None:
            try:
                # 创建池，指定基础配置和池配置
                连接池管理器._连接池实例 = pooling.MySQLConnectionPool(
                    pool_name=self.池配置字典.get('pool_name', 'mysql_pool'),
                    pool_size=self.池配置字典.get('pool_size', 32),
                    pool_reset_session=self.池配置字典.get('pool_reset_session', True),
                    **self.完整配置
                )
                logging.info("数据库连接池初始化成功，池大小: %s", self.池配置字典.get('pool_size', 32))
            except Exception as e:
                logging.error(f"数据库连接池初始化失败: {e}", exc_info=True)
                raise
    
    def 获取连接(self):
        """从连接池获取一个连接"""
        重试次数 = 3
        当前尝试 = 0
        
        while 当前尝试 < 重试次数:
            try:
                conn = 连接池管理器._连接池实例.get_connection()
                
                # 检查连接是否有效
                try:
                    conn.ping(reconnect=True, attempts=1, delay=0)
                except:
                    # 连接无效，关闭并重试
                    conn.close()
                    raise
                    
                return conn
            except Exception as e:
                当前尝试 += 1
                if 当前尝试 >= 重试次数:
                    logging.error(f"从连接池获取连接失败: {e}")
                    raise
                logging.warning(f"连接获取失败，正在重试 ({当前尝试}/{重试次数}): {e}")
                time.sleep(0.5)  # 短暂延迟后重试
    
    def 执行查询(self, sql, 参数=None, 缓存键=None, 缓存时间=None):
        """
        执行查询并返回结果，支持查询缓存和超时控制
        :param sql: SQL语句
        :param 参数: SQL参数
        :param 缓存键: 缓存键，如果提供则启用缓存
        :param 缓存时间: 缓存过期时间(秒)
        :return: 查询结果
        """
        # 检查缓存
        if 缓存键 and Config.启用缓存:
            缓存结果 = 缓存.获取(缓存键)
            if 缓存结果 is not None:
                return 缓存结果
        
        conn = None
        try:
            conn = self.获取连接()
            
            # 设置查询超时
            cursor = conn.cursor(dictionary=True)
            
            # 记录开始时间
            开始时间 = time.time()
            
            cursor.execute(sql, 参数 or ())
            
            # 检查是否超时
            if time.time() - 开始时间 > Config.查询超时:
                logging.warning(f"查询执行时间过长: {time.time() - 开始时间:.2f}秒, SQL: {sql}")
            
            result = cursor.fetchall()
            cursor.close()
            
            # 设置缓存
            if 缓存键 and Config.启用缓存:
                缓存.设置(缓存键, result, 缓存时间)
            
            return result
        except Exception as e:
            logging.error(f"查询执行失败: {e}, SQL: {sql}")
            raise
        finally:
            if conn:
                conn.close()  # 这里不是真正关闭连接，而是归还到连接池
    
    def 执行单条查询(self, sql, 参数=None, 缓存键=None, 缓存时间=None):
        """
        执行查询并返回单条结果，支持查询缓存和超时控制
        :param sql: SQL语句
        :param 参数: SQL参数
        :param 缓存键: 缓存键，如果提供则启用缓存
        :param 缓存时间: 缓存过期时间(秒)
        :return: 查询结果
        """
        # 检查缓存
        if 缓存键 and Config.启用缓存:
            缓存结果 = 缓存.获取(缓存键)
            if 缓存结果 is not None:
                return 缓存结果
        
        conn = None
        try:
            conn = self.获取连接()
            
            # 设置查询超时
            cursor = conn.cursor(dictionary=True)
            
            # 记录开始时间
            开始时间 = time.time()
            
            cursor.execute(sql, 参数 or ())
            
            # 检查是否超时
            if time.time() - 开始时间 > Config.查询超时:
                logging.warning(f"查询执行时间过长: {time.time() - 开始时间:.2f}秒, SQL: {sql}")
            
            result = cursor.fetchone()
            cursor.close()
            
            # 设置缓存
            if 缓存键 and Config.启用缓存:
                缓存.设置(缓存键, result, 缓存时间)
            
            return result
        except Exception as e:
            logging.error(f"单条查询执行失败: {e}, SQL: {sql}")
            raise
        finally:
            if conn:
                conn.close()  # 归还到连接池
    
    def 执行更新(self, sql, 参数=None, 清除缓存键=None):
        """
        执行更新操作，支持清除相关缓存
        :param sql: SQL语句
        :param 参数: SQL参数
        :param 清除缓存键: 需要清除的缓存键或键列表
        :return: 影响的行数
        """
        conn = None
        try:
            conn = self.获取连接()
            cursor = conn.cursor()
            
            # 记录开始时间
            开始时间 = time.time()
            
            cursor.execute(sql, 参数 or ())
            
            # 检查是否超时
            if time.time() - 开始时间 > Config.查询超时:
                logging.warning(f"更新执行时间过长: {time.time() - 开始时间:.2f}秒, SQL: {sql}")
            
            conn.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            
            # 清除相关缓存
            if 清除缓存键:
                if isinstance(清除缓存键, list):
                    for 键 in 清除缓存键:
                        缓存.清除(键)
                else:
                    缓存.清除(清除缓存键)
            
            return affected_rows
        except Exception as e:
            if conn:
                conn.rollback()  # 出错时回滚
            logging.error(f"更新执行失败: {e}, SQL: {sql}")
            raise
        finally:
            if conn:
                conn.close()  # 归还到连接池

class 数据库管理器(连接池管理器):
    """数据库连接和操作管理，使用连接池优化性能"""
    def __init__(self, 配置=None):
        super().__init__(配置)

# 提供免费账号和付费账号共用的功能
class 账号管理器基类:
    """账号管理基类，提供免费账号和付费账号共用的功能"""
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()
    
    def 检查获取频率(self, 标识, 是卡密=False):
        """
        检查获取频率，限制每三分钟只能获取一次
        :param 标识: 用户标识（机器码或卡密）
        :param 是卡密: 是否是卡密用户
        :return: (bool是否允许获取, str错误消息)
        """
        try:
            # 选择表名
            表名 = "卡密系统" if 是卡密 else "试用账号"
            字段名 = "卡密" if 是卡密 else "机器码"
            
            # 查询上次获取时间
            记录 = self.数据库.执行单条查询(
                f"SELECT 上次获取时间 FROM {表名} WHERE {字段名} = %s",
                (标识,)
            )
            
            当前时间 = datetime.datetime.now()
            
            if 记录 and 记录.get('上次获取时间'):
                上次获取时间 = 记录.get('上次获取时间')
                
                # 处理未来时间
                if 上次获取时间 > 当前时间:
                    上次获取时间 = 当前时间
                
                # 计算时间差（秒）
                时间差 = (当前时间 - 上次获取时间).total_seconds()
                
                # 根据是否是卡密用户选择不同的获取频率限制
                获取频率限制 = Config.付费账号配置['获取频率限制'] if 是卡密 else Config.试用配置['获取频率限制']
                
                # 如果时间差小于配置的限制时间，拒绝请求
                if 时间差 < 获取频率限制:
                    剩余时间 = 获取频率限制 - 时间差
                    剩余分钟 = int(剩余时间 // 60)
                    剩余秒 = int(剩余时间 % 60)
                    return False, f"您的额度还未使用完!无需再次获取，冷却{剩余分钟}分{剩余秒}秒"
            
            return True, None
            
        except Exception as e:
            logging.error(f"检查获取频率失败: {e}")
            return True, None  # 出错时默认允许获取，避免阻塞正常功能
    
    def 更新获取时间(self, 标识, 是卡密=False):
        """
        更新上次获取时间
        :param 标识: 用户标识（机器码或卡密）
        :param 是卡密: 是否是卡密用户
        """
        try:
            # 选择表名
            表名 = "卡密系统" if 是卡密 else "试用账号"
            字段名 = "卡密" if 是卡密 else "机器码"
            
            # 更新上次获取时间
            self.数据库.执行更新(
                f"UPDATE {表名} SET 上次获取时间 = %s WHERE {字段名} = %s",
                (datetime.datetime.now(), 标识)
            )
            
        except Exception as e:
            logging.error(f"更新获取时间失败: {e}")
    
    def 获取账号(self, 用户标识, 是卡密=False):
        """
        获取账号
        :param 用户标识: 用户标识（机器码或卡密）
        :param 是卡密: 是否是卡密用户
        :return: (bool成功, str邮箱, str访问令牌, str刷新令牌, str错误类型)
        """
        try:
            # 检查获取频率
            频率检查结果, 频率错误消息 = self.检查获取频率(用户标识, 是卡密)
            if not 频率检查结果:
                logging.info(f"用户 {用户标识} 获取频率限制: {频率错误消息}")
                return False, None, None, None, 频率错误消息
                
            # 统一使用付费号池表（邮箱系统）
            表名 = "邮箱系统"
            
            # 获取人数上限（根据用户类型获取不同的人数上限）
            人数上限 = Config.邮箱账号配置['付费账号最大人数'] if 是卡密 else Config.邮箱账号配置['免费账号最大人数']
            
            # 选择一个可用的邮箱账号，优先获取创建时间最新的
            邮箱信息 = self.数据库.执行单条查询(
                f"""
                SELECT 邮箱, 访问令牌, 刷新令牌, IFNULL(人数, 0) as 人数
                FROM {表名}
                WHERE 邮箱 IS NOT NULL
                AND 访问令牌 IS NOT NULL
                AND 刷新令牌 IS NOT NULL
                ORDER BY 创建时间 ASC
                LIMIT 1
                """
            )
            
            if not 邮箱信息:
                # 根据是否是卡密用户返回不同的提示信息
                错误消息 = '当前付费号池资源紧张,请稍后再试' if 是卡密 else '当前免费号池资源紧张,如需立即使用请升级会员'
                logging.warning(f"{表名} 表中无可用账号")
                return False, None, None, None, 错误消息
            
            邮箱 = 邮箱信息.get('邮箱')
            访问令牌 = 邮箱信息.get('访问令牌')
            刷新令牌 = 邮箱信息.get('刷新令牌')
            当前人数 = int(邮箱信息.get('人数', 0))
            
            try:
                # 先检查邮箱是否已存在于"已删除邮箱"表中
                已存在 = self.数据库.执行单条查询(
                    """
                    SELECT 邮箱 FROM 已删除邮箱 WHERE 邮箱 = %s
                    """,
                    (邮箱,)
                )
                
                # 增加当前人数
                新人数 = 当前人数 + 1
                
                # 如果超过人数上限，则删除账号
                if 新人数 >= 人数上限:
                    logging.info(f"邮箱 {邮箱} 人数({新人数})超过上限({人数上限})，执行删除")
                    # 如果邮箱不存在于"已删除邮箱"表中，则添加
                    if not 已存在:
                        # 将要删除的邮箱信息保存到"已删除邮箱"表
                        self.数据库.执行更新(
                            """
                            INSERT INTO 已删除邮箱(邮箱, 访问令牌, 刷新令牌, 创建时间, 用户标识)
                            VALUES (%s, %s, %s, NOW(), %s)
                            """,
                            (邮箱, 访问令牌, 刷新令牌, 用户标识)
                        )
                    
                    # 删除账号
                    self.数据库.执行更新(
                        f"DELETE FROM {表名} WHERE 邮箱 = %s",
                        (邮箱,)
                    )
                else:
                    # 没有达到人数上限，仅更新人数
                    更新结果 = self.数据库.执行更新(
                        f"UPDATE {表名} SET 人数 = %s WHERE 邮箱 = %s",
                        (新人数, 邮箱)
                    )
                
                # 更新获取时间
                self.更新获取时间(用户标识, 是卡密)
                
                logging.info(f"用户 {用户标识} 成功获取账号: {邮箱}")
                return True, 邮箱, 访问令牌, 刷新令牌, None
                
            except Exception as e:
                logging.error(f"邮箱处理失败: {e}", exc_info=True)
                return False, None, None, None, f"数据处理错误: {e}"
                
        except Exception as e:
            logging.error(f"获取邮箱账号出错: {e}", exc_info=True)
            return False, None, None, None, f"系统错误: {e}"

    def check_startup(self, 程序名, 备注, 机器码=None, 卡密=None, 系统配置=None):
        """
        启动检查方法，智能判断卡密是否已激活，未激活时自动执行激活
        :param 程序名: 程序名称
        :param 备注: 备注（必填参数）
        :param 机器码: 机器码（如果需要激活则必填）
        :param 卡密: 卡密，可选参数
        :param 系统配置: 系统配置，可选参数
        :return: (是否成功, 消息, 到期时间, 是否启用购买功能)
        """
        try:
            # 检查参数
            if not 程序名:
                return False, "缺少程序名称参数", None, None
                
            if not 备注:
                return False, "缺少备注参数", None, None

            # 如果有卡密，检查卡密逻辑
            if 卡密:
                # 检查号池状态
                if 系统配置 and not 系统配置.get('开启付费号池', 0):
                    return False, "当前付费号池资源紧张,请稍后再试", None, None
                
                # 检查卡密是否存在
                卡密信息 = self.数据库.执行单条查询(
                    "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                    (卡密,)
                )

                if not 卡密信息:
                    return False, "卡密不存在", None, None
                    
                # 判断是否为未激活的新卡密（通过首次登录时间是否为空判断）
                是新卡 = 卡密信息.get('首次登录时间') is None
                
                # 如果是新卡密，需要执行激活流程
                if 是新卡:
                    # 验证激活必要参数
                    if not 机器码:
                        return False, "激活卡密需要提供机器码", None, None
                    
                    # 判断使用哪个管理器激活卡密
                    if hasattr(self, '激活现有卡密'):
                        # 直接使用当前管理器
                        result, message, 到期时间 = self.激活现有卡密(
                            卡密, 机器码, 程序名, 备注
                        )
                    else:
                        # 创建付费账号管理器
                        付费管理器 = 付费账号管理器(self.数据库)
                        result, message, 到期时间 = 付费管理器.激活现有卡密(
                            卡密, 机器码, 程序名, 备注
                        )
                    
                    if result:
                        enable_purchase = 1  # 默认为1
                        
                        # 检查是否需要查询代理秘钥信息
                        if 备注 != "官方版":
                            代理秘钥信息 = self.数据库.执行单条查询(
                                "SELECT 启用购买功能 FROM 代理秘钥 WHERE 代理秘钥=%s",
                                (备注,)
                            )
                            if 代理秘钥信息 and '启用购买功能' in 代理秘钥信息:
                                enable_purchase = 代理秘钥信息['启用购买功能']
                        
                        return True, message, 到期时间, enable_purchase
                    else:
                        return False, message, None, None
                
                # 已激活卡密的处理逻辑
                
                # 如果程序名不为空且不匹配
                if 卡密信息.get('程序名') and 卡密信息.get('程序名') != 程序名:
                    return False, "此卡密不是本程序的卡密", None, None
                    
                # 如果程序名为空，更新程序名
                if 卡密信息.get('程序名') is None or 卡密信息.get('程序名') == '':
                    self.数据库.执行更新(
                        "UPDATE 卡密系统 SET 程序名=%s WHERE 卡密=%s",
                        (程序名, 卡密)
                    )
                    
                    # 重新获取卡密信息
                    卡密信息 = self.数据库.执行单条查询(
                        "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                        (卡密,)
                    )
                    
                # 检查卡密是否被禁用
                禁用状态 = 卡密信息.get('禁用卡密')
                if 禁用状态 is not None and int(禁用状态) > 0:
                    return False, "您的卡密已被禁用，由于被检测频繁大量刷号而未正常使用行为。", None, None
                
                # 检查卡密是否过期
                当前时间 = datetime.datetime.now()
                到期时间 = 卡密信息.get('到期时间')
                if 到期时间 and 当前时间 > 到期时间:
                    return False, "卡密已过期", 到期时间, None
                
                # 构建响应数据
                enable_purchase = 1  # 默认为1
                
                # 使用传入的备注参数查询代理秘钥表
                if 备注 and 备注 != "官方版":
                    代理秘钥信息 = self.数据库.执行单条查询(
                        "SELECT 启用购买功能 FROM 代理秘钥 WHERE 代理秘钥=%s",
                        (备注,)
                    )
                    if 代理秘钥信息 and '启用购买功能' in 代理秘钥信息:
                        enable_purchase = 代理秘钥信息['启用购买功能']
                
                # 检查通过，返回成功
                return True, "卡密验证通过", 到期时间, enable_purchase
            
            # 无卡密情况，只检查备注的启用购买功能
            enable_purchase = 1  # 默认为1
            
            # 使用传入的备注参数查询代理秘钥表
            if 备注 and 备注 != "官方版":
                代理秘钥信息 = self.数据库.执行单条查询(
                    "SELECT 启用购买功能 FROM 代理秘钥 WHERE 代理秘钥=%s",
                    (备注,)
                )
                if 代理秘钥信息 and '启用购买功能' in 代理秘钥信息:
                    enable_purchase = 代理秘钥信息['启用购买功能']
            
            # 无卡密情况下返回成功
            return True, "启动检查通过", None, enable_purchase
                
        except Exception as e:
            logging.error(f"启动检查错误: {str(e)}", exc_info=True)
            return False, f"服务器错误: {str(e)}", None, None

class 试用账号管理器(账号管理器基类):
    """试用账号管理相关功能，包含IP限制逻辑"""
    def __init__(self, 数据库=None, 配置=None):
        super().__init__(数据库)
        self.配置 = 配置 or Config.试用配置
        self.IP限制配置 = Config.IP限制

    def 检查IP限制(self, ip):
        """
        检查IP是否超过限制
        :param ip: IP地址
        :return: (是否通过, 原因)
        """
        if not self.IP限制配置['启用IP限制']:
            return True, "IP限制未启用"

        try:
            # 先查询IP是否已存在
            ip_record = self.数据库.执行单条查询(
                "SELECT * FROM 试用账号IP WHERE 试用IP=%s",
                (ip,)
            )

            if ip_record:
                # 检查IP是否被禁用
                if ip_record['IP是否被禁用'] != 0:
                    return False, "IP已被禁用"

                # 检查设备数量是否超过限制
                if ip_record['设备数量'] > self.IP限制配置['每IP最大设备数']:
                    logging.info(f"IP已达到最大设备数量限制: {self.IP限制配置['每IP最大设备数']}")
                    # return False, f"IP已达到最大设备数量限制: {self.IP限制配置['每IP最大设备数']}"
                    # 虚假提醒
                    return False, "当前免费号池资源紧张,如需立即使用请升级会员"

                # 更新最近登录时间
                self.数据库.执行更新(
                    "UPDATE 试用账号IP SET 最近登录时间=%s WHERE 试用IP=%s",
                    (datetime.datetime.now(), ip)
                )
            else:
                # 创建IP记录
                self.数据库.执行更新(
                    "INSERT INTO 试用账号IP(试用IP, 设备数量, IP是否被禁用, 首次登录时间, 最近登录时间) VALUES(%s, 0, 0, %s, %s)",
                    (ip, datetime.datetime.now(), datetime.datetime.now())
                )

            return True, "IP检查通过"
        except Exception as e:
            logging.error(f"IP限制检查失败: {e}")
            return False, f"IP检查出错: {str(e)}"

    def 增加IP设备数(self, ip):
        """
        增加IP关联的设备数量
        :param ip: IP地址
        :return: 是否成功
        """
        try:
            # 查询IP记录
            ip_record = self.数据库.执行单条查询(
                "SELECT 设备数量 FROM 试用账号IP WHERE 试用IP=%s",
                (ip,)
            )

            if ip_record:
                # 更新设备数量
                self.数据库.执行更新(
                    "UPDATE 试用账号IP SET 设备数量=%s, 最近登录时间=%s WHERE 试用IP=%s",
                    (ip_record['设备数量'] + 1, datetime.datetime.now(), ip)
                )
            else:
                # 创建IP记录
                self.数据库.执行更新(
                    "INSERT INTO 试用账号IP(试用IP, 设备数量, IP是否被禁用, 首次登录时间, 最近登录时间) VALUES(%s, 1, 0, %s, %s)",
                    (ip, datetime.datetime.now(), datetime.datetime.now())
                )

            return True
        except Exception as e:
            logging.error(f"增加IP设备数量失败: {e}")
            return False

    def 验证试用账号(self, 用户):
        """
        验证试用账号
        :param 用户: 用户信息对象
        :return: (是否验证通过, 消息, 邮箱, 访问令牌, 刷新令牌)
        """
        try:
            # 先检查IP限制
            ip_check, ip_message = self.检查IP限制(用户.IP)
            if not ip_check:
                logging.info(f"IP检查未通过: {用户.IP}, 原因: {ip_message}")
                return False, ip_message, None, None, None

            # 先查询是否已存在该机器码的记录
            existing_record = self.数据库.执行单条查询(
                "SELECT 禁用机器码, 获取次数 FROM 试用账号 WHERE 机器码=%s",
                (用户.机器码,)
            )

            if existing_record:
                # 检查机器码是否被禁用
                if existing_record['禁用机器码'] != 0:
                    logging.info(f"机器码已被禁用: {用户.机器码}")
                    return False, "机器码已被禁用", None, None, None

                # 检查获取次数是否超过限制
                if existing_record['获取次数'] >= self.配置['最大获取次数']:
                    logging.info(f"机器码 {用户.机器码} 已超过最大获取次数: {self.配置['最大获取次数']}")
                    # return False, f"已超过最大获取次数限制: {self.配置['最大获取次数']}", None, None, None
                    #虚假提醒
                    return False, "当前免费号池资源紧张,如需立即使用请升级会员", None, None, None

                # 更新现有记录，包括IP
                self.数据库.执行更新(
                    "UPDATE 试用账号 SET 最近登录=%s, 当前IP=%s WHERE 机器码=%s",
                    (datetime.datetime.now(), 用户.IP, 用户.机器码)
                )

                用户.免费已试用次数 = existing_record['获取次数']
            else:
                logging.info(f"新机器码注册: {用户.机器码}, IP: {用户.IP}")
                # 创建新记录时，增加IP设备数量
                self.增加IP设备数(用户.IP)

                # 插入新记录，包括IP
                self.数据库.执行更新(
                    "INSERT INTO 试用账号(机器码, 创建时间, 最近登录, 获取次数, 禁用机器码, 当前IP) VALUES(%s, %s, %s, 0, 0, %s)",
                    (用户.机器码, datetime.datetime.now(), datetime.datetime.now(), 用户.IP)
                )

                用户.免费已试用次数 = 0
            
            # 验证通过后，获取账号
            获取成功, 邮箱, 访问令牌, 刷新令牌, 错误类型 = self.获取账号(用户.机器码, False)
        
            if 获取成功:
                # 增加试用次数
                self.增加试用次数(用户.机器码)
                return True, f"验证成功，已获取邮箱: {邮箱}", 邮箱, 访问令牌, 刷新令牌
            else:
                logging.warning(f"机器码 {用户.机器码} 获取账号失败: {错误类型}")
                return False, 错误类型, None, None, None
                
        except Exception as e:
            logging.error(f"验证试用账号失败: {e}", exc_info=True)
            return False, f"服务器错误: {str(e)}", None, None, None

    def 增加试用次数(self, 机器码):
        """
        增加试用次数
        :param 机器码: 机器码
        :return: (是否成功, 消息)
        """
        try:
            # 查询当前获取次数
            record = self.数据库.执行单条查询(
                "SELECT 获取次数 FROM 试用账号 WHERE 机器码=%s",
                (机器码,)
            )

            if record:
                # 先检查是否已达到最大次数限制
                if record['获取次数'] >= self.配置['最大获取次数']:
                    logging.info(f"机器码 {机器码} 已达到最大获取次数: {self.配置['最大获取次数']}")
                    # return False, f"已达到最大获取次数限制: {self.配置['最大获取次数']}"
                    #虚假提醒
                    return False, "当前免费号池资源紧张,如需立即使用请升级会员"
                    
                # 更新获取次数
                self.数据库.执行更新(
                    "UPDATE 试用账号 SET 获取次数=%s WHERE 机器码=%s",
                    (record['获取次数'] + 1, 机器码)
                )
                return True, "增加试用次数成功"
            else:
                return False, "机器码不存在"
        except Exception as e:
            logging.error(f"增加试用次数失败: {e}")
            return False, f"增加试用次数失败: {str(e)}"

    def 更新IP(self, 机器码, ip):
        """
        更新机器码关联的IP
        :param 机器码: 机器码
        :param ip: IP地址
        :return: 是否成功
        """
        try:
            self.数据库.执行更新(
                "UPDATE 试用账号 SET 当前IP=%s WHERE 机器码=%s",
                (ip, 机器码)
            )
            return True
        except Exception as e:
            logging.error(f"更新IP失败: {e}")
            return False

class 付费账号管理器(账号管理器基类):
    """付费账号管理相关功能"""
    def __init__(self, 数据库=None, 配置=None):
        super().__init__(数据库)
        self.配置 = 配置 or Config.试用配置

    # 添加清空机器码方法
    def 清空机器码(self, 卡密):
        """
        清空卡密对应的机器码
        :param 卡密: 卡密
        :return: (是否成功, 消息)
        """
        try:
            # 检查卡密是否存在
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )
            
            if not 卡密信息:
                return False, "卡密不存在"
                
            # 清空机器码
            self.数据库.执行更新(
                "UPDATE 卡密系统 SET 机器码=NULL WHERE 卡密=%s",
                (卡密,)
            )
            
            return True, "机器码已清空"
            
        except Exception as e:
            logging.error(f"清空机器码失败: {e}")
            return False, f"清空机器码失败: {str(e)}"

    def 检查调用限制(self, 卡密信息):
        """
        检查卡密调用限制
        :param 卡密信息: 卡密信息
        :return: (是否允许调用, 下次重置时间)
        """
        try:
            当前时间 = datetime.datetime.now()

            # 获取调用次数相关信息
            上次重置时间 = 卡密信息.get('上次重置时间')
            调用次数 = 卡密信息.get('调用次数', 0) or 0  # 处理None值

            # 检查二十分钟调用次数
            二十分钟开始时间 = 卡密信息.get('二十分钟开始时间')
            二十分钟调用次数 = 卡密信息.get('二十分钟调用次数', 0) or 0  # 处理None值

            # 处理未来时间
            if 上次重置时间 and 上次重置时间 > 当前时间:
                上次重置时间 = 当前时间
                调用次数 = 0
                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 上次重置时间=%s, 调用次数=%s WHERE 卡密=%s",
                    (当前时间, 调用次数, 卡密信息.get('卡密'))
                )

            if 二十分钟开始时间 and 二十分钟开始时间 > 当前时间:
                二十分钟开始时间 = 当前时间
                二十分钟调用次数 = 0
                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 二十分钟开始时间=%s, 二十分钟调用次数=%s WHERE 卡密=%s",
                    (当前时间, 二十分钟调用次数, 卡密信息.get('卡密'))
                )

            # 将硬编码的时间值替换为计算值
            二十分钟秒数 = 20 * 60  # 20分钟 = 1200秒
            十分钟秒数 = 10 * 60    # 10分钟 = 600秒
            
            # 如果超过20分钟，重置二十分钟计数器
            if not 二十分钟开始时间 or (当前时间 - 二十分钟开始时间).total_seconds() >= 二十分钟秒数:
                二十分钟调用次数 = 0
                二十分钟开始时间 = 当前时间

                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 二十分钟开始时间=%s, 二十分钟调用次数=%s WHERE 卡密=%s",
                    (二十分钟开始时间, 二十分钟调用次数, 卡密信息.get('卡密'))
                )
            # 检查二十分钟内调用次数是否超过限制
            elif 二十分钟调用次数 >= Config.付费账号配置['二十分钟最大调用次数']:
                # 禁用卡密
                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 禁用卡密=1 WHERE 卡密=%s",
                    (卡密信息.get('卡密'),)
                )
                return False, None

            # 计算下次重置时间 - 总是基于上次重置时间加10分钟
            下次重置时间 = None
            if 上次重置时间:
                下次重置时间 = 上次重置时间 + datetime.timedelta(minutes=10)

            # 检查是否需要重置计数器（10分钟）
            if not 上次重置时间 or (当前时间 - 上次重置时间).total_seconds() >= 十分钟秒数:
                调用次数 = 0
                上次重置时间 = 当前时间  # 更新为当前时间
                下次重置时间 = 当前时间 + datetime.timedelta(minutes=10)

                # 更新数据库中的重置时间和调用次数
                self.数据库.执行更新(
                    "UPDATE 卡密系统 SET 上次重置时间=%s, 调用次数=%s WHERE 卡密=%s",
                    (上次重置时间, 调用次数, 卡密信息.get('卡密'))
                )

            # 检查是否超过限制
            if 调用次数 >= Config.付费账号配置['十分钟最大调用次数']:
                return False, 下次重置时间

            return True, 下次重置时间

        except Exception as e:
            logging.error(f"检查调用限制失败: {e}")
            return True, None  # 出错时允许调用，避免阻塞正常功能

    def 检查卡密有效性(self, 卡密, 机器码):
        """
        检查卡密是否有效，并在首次使用时自动绑定机器码（合并了原激活卡密功能）
        :param 卡密: 卡密
        :param 机器码: 机器码
        :return: (是否有效, 消息或账号信息, 卡密信息)
        """
        try:
            当前时间 = datetime.datetime.now()

            # 查询卡密信息
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )

            if not 卡密信息:
                logging.info(f"卡密不存在: {卡密}")
                return False, "卡密不存在", None

            # 检查卡密是否被禁用 - 将null和0都视为未禁用
            禁用状态 = 卡密信息.get('禁用卡密')
            if 禁用状态 is not None and int(禁用状态) > 0:
                logging.info(f"卡密已被禁用: {卡密}")
                return False, "您的卡密已被禁用，由于被检测频繁大量刷号而未正常使用行为。", 卡密信息

            # 检查卡密是否过期
            到期时间 = 卡密信息.get('到期时间')
            if 到期时间 and 当前时间 > 到期时间:
                logging.info(f"卡密已过期: {卡密}, 过期时间: {到期时间}")
                return False, "卡密已过期", 卡密信息

            # 处理卡密的机器码绑定
            if 卡密信息.get('机器码'):
                # 如果已绑定，检查是否是同一机器码
                现有机器码 = 卡密信息.get('机器码', '')
                最大数量 = 卡密信息.get('最大数量', 1)
                
                # 快速检查机器码是否已存在（不需要分割整个字符串）
                机器码已存在 = f"-{机器码}" in f"-{现有机器码}-" or 现有机器码 == 机器码
                
                # 只有在需要验证数量时才分割机器码列表
                if not 机器码已存在 and 最大数量 > 1:
                    现有机器码列表 = 现有机器码.split('-') if 现有机器码 else []
                    if len(现有机器码列表) >= 最大数量:
                        logging.info(f"卡密 {卡密} 已达到最大机器码数量: {最大数量}")
                        return False, "验证失败: 此卡密不可同时登录, 请先正常关闭另外一个已登录设备再进行登录", 卡密信息
                
                # 更新登录信息
                try:
                    if not 机器码已存在:
                        # 使用追加方式更新机器码
                        新机器码 = f"{现有机器码}-{机器码}" if 现有机器码 else 机器码
                        logging.info(f"卡密 {卡密} 绑定新机器码: {机器码}")
                        self.数据库.执行更新(
                            """
                            UPDATE 卡密系统 SET
                                机器码=%s,
                                最近登录=%s,
                                本月登录次数=CASE
                                    WHEN MONTH(最近登录)=MONTH(%s) THEN 本月登录次数+1
                                    ELSE 1
                                END
                            WHERE 卡密=%s
                            """,
                            (新机器码, 当前时间, 当前时间, 卡密)
                        )
                    else:
                        # 更新已有机器码的登录信息
                        self.数据库.执行更新(
                            """
                            UPDATE 卡密系统 SET
                                最近登录=%s,
                                本月登录次数=CASE
                                    WHEN MONTH(最近登录)=MONTH(%s) THEN 本月登录次数+1
                                    ELSE 1
                                END
                            WHERE 卡密=%s
                            """,
                            (当前时间, 当前时间, 卡密)
                        )
                except Exception as e:
                    logging.error(f"更新卡密信息失败: {e}", exc_info=True)
                    return False, f"更新卡密信息失败: {str(e)}", 卡密信息
            else:
                # 机器码为空，并非首次登录,也有可能是清除了机器码为了换绑
                logging.info(f"卡密 {卡密} 首次绑定机器码: {机器码}")
                self.数据库.执行更新(
                    """
                    UPDATE 卡密系统 SET
                        机器码=%s,
                        最近登录=%s,
                        本月登录次数=CASE
                            WHEN MONTH(最近登录)=MONTH(%s) THEN 本月登录次数+1
                            ELSE 1
                        END
                    WHERE 卡密=%s
                    """,
                    (机器码, 当前时间, 当前时间, 卡密)
                )
                
                # 重新获取更新后的卡密信息
                卡密信息 = self.数据库.执行单条查询(
                    "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                    (卡密,)
                )

            # 检查使用次数
            调用限制结果, 下次重置时间 = self.检查调用限制(卡密信息)
            if 调用限制结果:
                # 获取账号
                获取成功, 邮箱, 访问令牌, 刷新令牌, 错误类型 = self.获取账号(卡密, True)
            
                # 根据获取结果返回不同响应
                if 获取成功:
                    # 更新卡密使用记录
                    self.更新卡密使用记录(卡密, 机器码)
                    
                    # 返回一个包含完整账号信息的字典
                    账号信息 = {
                        "邮箱": 邮箱,
                        "访问令牌": 访问令牌,
                        "刷新令牌": 刷新令牌
                    }
                    logging.info(f"卡密 {卡密} 成功获取账号: {邮箱}")
                    return True, 账号信息, 卡密信息
                else:
                    logging.warning(f"卡密 {卡密} 获取账号失败: {错误类型}")
                    return False, 错误类型, 卡密信息
            else:
                # 设置友好的错误消息，包含下次重置时间
                错误消息 = f"为避免被恶意刷取账号，每10分钟最多获取{Config.付费账号配置['十分钟最大调用次数']}次账号"
                if 下次重置时间:
                    错误消息 += f"\n下次重置时间：{下次重置时间.strftime('%H:%M:%S')}"
                logging.info(f"卡密 {卡密} 调用频率限制: {错误消息}")
                return False, 错误消息, 卡密信息

        except Exception as e:
            logging.error(f"检查卡密有效性失败: {e}", exc_info=True)
            return False, f"验证卡密时出错: {str(e)}", None

    # 生成新卡密并激活，绑定机器码
    def 卡密激活(self, 机器码, 程序名, 类型, 备注="官方版"):
        """
        生成并激活卡密，绑定机器码
        :param 机器码: 机器码
        :param 程序名: 程序名
        :param 类型: 卡密类型，必须指定
        :param 备注: 卡密备注，可选，默认为"官方版"
        :return: (是否成功, 消息, 卡密)
        """
        try:
            当前时间 = datetime.datetime.now()
            
            # 生成随机卡密(10位字母数字混合)
            import random
            import string
            字符集 = string.ascii_letters + string.digits
            卡密 = ''.join(random.choice(字符集) for _ in range(10))
            
            # 计算到期时间
            if 类型 == "时卡":
                到期时间 = 当前时间 + datetime.timedelta(hours=1)
            elif 类型 == "天卡":
                到期时间 = 当前时间 + datetime.timedelta(days=1)
            elif 类型 == "周卡":
                到期时间 = 当前时间 + datetime.timedelta(days=7)
            elif 类型 == "月卡":
                到期时间 = 当前时间 + datetime.timedelta(days=30)
            elif 类型 == "年卡":
                到期时间 = 当前时间 + datetime.timedelta(days=365)
            else:
                return False, "验证失败: 无效的卡密类型", None
            
            # 创建新卡密记录
            self.数据库.执行更新(
                """
                INSERT INTO 卡密系统 (
                    卡密, 机器码, 程序名, 首次登录时间, 最近登录,
                    到期时间, 本月登录次数, 最大数量, 类型, 备注
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (卡密, 机器码, 程序名, 当前时间, 当前时间, 到期时间, 1, 1, 类型, 备注)
            )
            
            # 格式化到期时间
            到期时间显示 = 到期时间.strftime("%Y-%m-%d %H:%M:%S")
            
            return True, f"卡密生成成功,到期时间={到期时间显示}", 卡密
            
        except Exception as e:
            logging.error(f"卡密生成失败: {e}")
            return False, f"生成失败: {str(e)}", None

    def 更新卡密使用记录(self, 卡密, 机器码):
        """
        更新卡密使用记录
        :param 卡密: 卡密
        :param 机器码: 机器码
        """
        try:
            当前时间 = datetime.datetime.now()

            # 获取当前的调用信息
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )

            if not 卡密信息:
                logging.error(f"未找到卡密信息: {卡密}")
                return

            # 获取当前值
            调用次数 = 卡密信息.get('调用次数', 0) or 0  # 处理None值
            总调用次数 = 卡密信息.get('总调用次数', 0) or 0  # 处理None值
            二十分钟调用次数 = 卡密信息.get('二十分钟调用次数', 0) or 0  # 处理None值
            本月登录次数 = 卡密信息.get('本月登录次数', 0) or 0  # 处理None值

            # 增加计数
            调用次数 += 1
            总调用次数 += 1
            二十分钟调用次数 += 1
            本月登录次数 += 1

            # 如果时间字段为未来时间，则重置为当前时间
            上次重置时间 = 卡密信息.get('上次重置时间')
            if not 上次重置时间 or 上次重置时间 > 当前时间:
                上次重置时间 = 当前时间

            二十分钟开始时间 = 卡密信息.get('二十分钟开始时间')
            if not 二十分钟开始时间 or 二十分钟开始时间 > 当前时间:
                二十分钟开始时间 = 当前时间

            # 将硬编码的时间值替换为计算值
            十分钟秒数 = 10 * 60  # 10分钟 = 600秒
            
            # 检查是否需要重置计数器（10分钟）
            if 上次重置时间 and (当前时间 - 上次重置时间).total_seconds() >= 十分钟秒数:
                调用次数 = 1  # 重置为1而不是0，因为当前这次调用算一次
                上次重置时间 = 当前时间  # 从当前时间重新开始计时

            # 更新数据库
            self.数据库.执行更新(
                """
                UPDATE 卡密系统
                SET 调用次数=%s, 总调用次数=%s, 上次重置时间=%s,
                    二十分钟调用次数=%s, 二十分钟开始时间=%s,
                    最近登录=%s, 机器码=%s, 本月登录次数=%s
                WHERE 卡密=%s
                """,
                (调用次数, 总调用次数, 当前时间, 二十分钟调用次数, 二十分钟开始时间,
                 当前时间, 机器码, 本月登录次数, 卡密)
            )

        except Exception as e:
            logging.error(f"更新卡密使用记录失败: {e}")

    def 获取卡密信息(self, 卡密=None, 机器码=None):
        """
        获取卡密信息
        :param 卡密: 卡密
        :param 机器码: 机器码
        :return: 卡密信息
        """
        try:
            if 卡密:
                return self.数据库.执行单条查询(
                    "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                    (卡密,)
                )
            elif 机器码:
                return self.数据库.执行单条查询(
                    "SELECT * FROM 卡密系统 WHERE 机器码=%s AND 是否使用=1",
                    (机器码,)
                )
            return None
        except Exception as e:
            logging.error(f"获取卡密信息失败: {e}")
            return None

    def 激活现有卡密(self, 卡密, 机器码, 程序名, 备注="官方版"):
        """
        激活已存在的卡密，绑定机器码和程序名
        :param 卡密: 卡密
        :param 机器码: 机器码
        :param 程序名: 程序名
        :param 备注: 备注，可选，默认为"官方版"
        :return: (是否成功, 消息, 到期时间)
        """
        try:
            当前时间 = datetime.datetime.now()
            
            # 获取卡密信息
            卡密信息 = self.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (卡密,)
            )
            
            if not 卡密信息:
                return False, "卡密不存在", None
                
            # 检查卡密是否已激活
            if 卡密信息.get('首次登录时间') is not None:
                return False, "卡密已被激活", 卡密信息.get('到期时间')
                
            # 优先使用数据库中已有的到期时间
            到期时间 = 卡密信息.get('到期时间')
            if 到期时间:
                卡密类型 = 卡密信息.get('类型')
                if not 卡密类型:
                    return False, "卡密类型不存在，无法激活", None
            else:
                # 获取卡密类型
                卡密类型 = 卡密信息.get('类型')
                
                # 检查类型是否存在
                if not 卡密类型:
                    return False, "卡密类型不存在，无法激活", None
                    
                # 检查类型是否有效
                有效类型列表 = ["时卡", "天卡", "周卡", "月卡", "年卡"]
                if 卡密类型 not in 有效类型列表:
                    return False, f"无效的卡密类型：{卡密类型}", None
                
                # 根据类型计算到期时间
                if 卡密类型 == "时卡":
                    到期时间 = 当前时间 + datetime.timedelta(hours=1)
                elif 卡密类型 == "天卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=1)
                elif 卡密类型 == "周卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=7)
                elif 卡密类型 == "月卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=30)
                elif 卡密类型 == "年卡":
                    到期时间 = 当前时间 + datetime.timedelta(days=365)
            
            # 更新卡密信息
            self.数据库.执行更新(
                """
                UPDATE 卡密系统 SET
                    机器码=%s,
                    程序名=%s,
                    首次登录时间=%s,
                    最近登录=%s,
                    到期时间=%s,
                    本月登录次数=1,
                    最大数量=1,
                    类型=%s,
                    备注=%s
                WHERE 卡密=%s
                """,
                (机器码, 程序名, 当前时间, 当前时间, 到期时间, 卡密类型, 备注, 卡密)
            )
            
            # 格式化到期时间
            到期时间显示 = 到期时间.strftime("%Y-%m-%d %H:%M:%S")
            
            return True, f"卡密激活成功,到期时间={到期时间显示}", 到期时间
            
        except Exception as e:
            logging.error(f"激活现有卡密失败: {e}")
            return False, f"激活失败: {str(e)}", None

# 添加系统配置管理器类
class 系统配置管理器:
    """系统配置管理器，负责获取数据库中的动态配置"""
    def __init__(self, 数据库=None):
        self.数据库 = 数据库 or 数据库管理器()

    def 检查系统配置(self):
        """
        检查全局配置（开启免费号池和付费号池，以及Windows和Mac版本号）
        :return: (开启免费号池, 开启付费号池, Windows版本号, Mac版本号)
        """
        try:
            # 从cursorpro表查询配置
            配置 = self.数据库.执行单条查询(
                "SELECT 开启免费号池, 开启付费号池, 网络版本号_Windows, 网络版本号_Mac FROM cursorpro ORDER BY id DESC LIMIT 1"
            )

            if 配置:
                开启免费号池 = 配置.get('开启免费号池', 0)
                开启付费号池 = 配置.get('开启付费号池', 0)
                网络版本号_Windows = 配置.get('网络版本号_Windows', '')
                网络版本号_Mac = 配置.get('网络版本号_Mac', '')
                return 开启免费号池, 开启付费号池, 网络版本号_Windows, 网络版本号_Mac

            return 0, 0, '', ''
        except Exception as e:
            logging.error(f"检查系统配置失败: {e}")
            return 0, 0, '', ''  # 默认都关闭，版本号为空字符串

#账号数据是:邮箱 访问令牌 刷新令牌,用户标识:免费则是机器码,付费则是卡密
class API服务:
    """API服务类，处理HTTP请求"""
    def __init__(self):
        self.app = Flask(__name__)
        self.数据库 = 数据库管理器()
        self.系统配置 = 系统配置管理器(self.数据库)  # 添加系统配置管理器实例
        self.试用管理器 = 试用账号管理器(self.数据库)
        self.付费管理器 = 付费账号管理器(self.数据库)
        
        # 定期任务设置
        self.设置定期任务()
        
        # 注册路由
        self.注册路由()
        
        logging.info("API服务初始化完成，连接池已创建")
    
    def 设置定期任务(self):
        """设置定期执行的任务"""
        import threading
        
        def 缓存清理任务():
            """定期清理过期缓存的任务"""
            while True:
                try:
                    缓存.清理过期缓存()
                except Exception as e:
                    logging.error(f"缓存清理任务执行失败: {e}")
                time.sleep(60)  # 每分钟执行一次
        
        # 启动缓存清理线程
        清理线程 = threading.Thread(target=缓存清理任务, daemon=True)
        清理线程.start()
        logging.info("缓存清理任务已启动")

    def 注册路由(self):
        """注册API路由"""
        self.app.route('/verify_trial', methods=['POST'])(self.verify_trial)
        self.app.route('/increase_trial', methods=['POST'])(self.increase_trial)
        # 付费相关路由
        self.app.route('/verify_paid', methods=['POST'])(self.verify_paid)
        # 新增卡密激活路由
        self.app.route('/activate_key', methods=['POST'])(self.activate_key)
        # 新增启动检查路由
        self.app.route('/check_startup', methods=['POST'])(self.check_startup)
        # 添加清空机器码路由
        self.app.route('/clear_machine_code', methods=['POST'])(self.clear_machine_code)

    # 验证试用账号API端点
    def verify_trial(self):
        """
        验证试用账号API端点
        请求参数:
        - machine_code: 机器码
        - ip: IP地址（必填）
        - version: 客户端版本号（必填）
        - platform: 平台（必填，Windows或Mac）
        返回:
        - success: 是否成功
        - message: 消息
        - trial_count: 当前试用次数
        - account_info: 账号信息（如果成功）
        - need_update: 是否需要更新（可选）
        """
        try:
            data = request.get_json()
            if not data:
                logging.warning("验证试用账号API接收到空数据")
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据',
                    'trial_count': 0
                }), 400

            # 验证必填参数
            必填参数 = ['machine_code', 'ip', 'version', 'platform']
            缺少参数 = [param for param in 必填参数 if param not in data]
            if 缺少参数:
                缺少参数字符串 = ', '.join(缺少参数)
                logging.warning(f"验证试用账号API缺少必填参数: {缺少参数字符串}")
                return jsonify({
                    'success': False,
                    'message': f'缺少参数: {缺少参数字符串}',
                    'trial_count': 0
                }), 400

            # 检查系统配置 - 使用系统配置管理器
            开启免费号池, _, 网络版本号_Windows, 网络版本号_Mac = self.系统配置.检查系统配置()
            
            # 检查版本号
            客户端版本 = data['version']
            平台 = data['platform']
            服务器版本 = 网络版本号_Windows if 平台 == 'Windows' else 网络版本号_Mac
            
            # 如果版本号不为空且客户端版本低于服务器版本，则提示更新
            if 服务器版本 and 客户端版本 < 服务器版本:
                logging.info(f"客户端版本过低: 当前={客户端版本}, 最新={服务器版本}")
                return jsonify({
                    'success': False,
                    'message': f'当前版本过低，请更新到最新版本 {服务器版本}',
                    'trial_count': 0,
                    'need_update': True,
                    'latest_version': 服务器版本
                })
            
            if 开启免费号池 == 0:
                logging.info("免费号池已关闭")
                return jsonify({
                    'success': False,
                    'message': '当前免费号池资源紧张,如需立即使用请升级会员',
                    'trial_count': 0
                })

            # 创建用户信息对象
            用户 = 用户信息(机器码=data['machine_code'], IP=data['ip'])

            # 验证试用账号并获取账号
            result, message, 邮箱, 访问令牌, 刷新令牌 = self.试用管理器.验证试用账号(用户)

            if result:
                账号信息 = {
                    "邮箱": 邮箱,
                    "访问令牌": 访问令牌,
                    "刷新令牌": 刷新令牌
                }
                logging.info(f"试用账号验证成功: {data['machine_code']}")
                return jsonify({
                    'success': True,
                    'message': message,
                    'trial_count': 用户.免费已试用次数 + 1,  # 已经在验证方法中增加了次数
                    'account_info': 账号信息
                })
            else:
                logging.info(f"试用账号验证失败: {data['machine_code']}, 原因: {message}")
                return jsonify({
                    'success': False,
                    'message': message,
                    'trial_count': 0
                })
        except Exception as e:
            logging.error(f"验证试用账号API错误: {e}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}',
                'trial_count': 0
            }), 500

    # 试用次数API端点
    def increase_trial(self):
        """
        增加试用次数API端点
        请求参数:
        - machine_code: 机器码
        - ip: IP地址（必填）
        返回:
        - success: 是否成功
        - message: 消息
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'machine_code' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少机器码参数'
                }), 400

            if 'ip' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少IP参数'
                }), 400

            # 检查系统配置
            开启免费号池, _ = self.付费管理器.检查系统配置()
            if 开启免费号池 == 0:
                return jsonify({
                    'success': False,
                    'message': '当前免费号池资源紧张,如需立即使用请升级会员'
                })

            # 先检查IP限制
            ip_check, ip_message = self.试用管理器.检查IP限制(data['ip'])
            if not ip_check:
                return jsonify({
                    'success': False,
                    'message': ip_message
                })

            # 更新IP
            self.试用管理器.更新IP(data['machine_code'], data['ip'])

            # 增加试用次数
            result, message = self.试用管理器.增加试用次数(data['machine_code'])

            if result:
                return jsonify({
                    'success': True,
                    'message': message
                })
            else:
                return jsonify({
                    'success': False,
                    'message': message
                })
        except Exception as e:
            logging.error(f"API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 验证付费账号API端点
    def verify_paid(self):
        """
        验证付费账号API端点
        请求参数:
        - key: 卡密
        - machine_code: 机器码
        - version: 客户端版本号（必填）
        - platform: 平台（必填，Windows或Mac）
        - app_name: 程序名称（可选，用于记录）
        - remark: 备注（可选，用于记录）
        返回:
        - success: 是否成功
        - message: 消息
        - key_info: 卡密信息
        - account_info: 账号信息（邮箱、访问令牌、刷新令牌）
        - need_update: 是否需要更新（可选）
        """
        try:
            data = request.get_json()
            if not data:
                logging.warning("验证付费账号API接收到空数据")
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            必填参数 = ['key', 'machine_code', 'version', 'platform']
            缺少参数 = [param for param in 必填参数 if param not in data]
            if 缺少参数:
                缺少参数字符串 = ', '.join(缺少参数)
                logging.warning(f"验证付费账号API缺少必填参数: {缺少参数字符串}")
                return jsonify({
                    'success': False,
                    'message': f'缺少参数: {缺少参数字符串}'
                }), 400

            # 检查系统配置 - 使用系统配置管理器
            _, 开启付费号池, 网络版本号_Windows, 网络版本号_Mac = self.系统配置.检查系统配置()
            
            # 检查版本号
            客户端版本 = data['version']
            平台 = data['platform']
            服务器版本 = 网络版本号_Windows if 平台 == 'Windows' else 网络版本号_Mac
            
            # 如果版本号不为空且客户端版本低于服务器版本，则提示更新
            if 服务器版本 and 客户端版本 < 服务器版本:
                logging.info(f"客户端版本过低: 当前={客户端版本}, 最新={服务器版本}")
                return jsonify({
                    'success': False,
                    'message': f'当前版本过低，请更新到最新版本 {服务器版本}',
                    'need_update': True,
                    'latest_version': 服务器版本
                })
            
            if 开启付费号池 == 0:
                logging.info("付费号池已关闭")
                return jsonify({
                    'success': False,
                    'message': '当前付费号池资源紧张,请稍后再试'
                })

            # 先检查卡密是否存在
            卡密存在 = self.付费管理器.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s",
                (data['key'],)
            )

            if not 卡密存在:
                logging.info(f"卡密不存在: {data['key']}")
                return jsonify({
                    'success': False,
                    'message': '卡密不存在'
                })

            # 再检查程序名是否匹配
            卡密信息 = self.付费管理器.数据库.执行单条查询(
                "SELECT * FROM 卡密系统 WHERE 卡密=%s AND 程序名=%s",
                (data['key'], data['app_name'])
            )

            if not 卡密信息:
                logging.info(f"卡密程序名不匹配: {data['key']}, 期望: {data['app_name']}")
                return jsonify({
                    'success': False,
                    'message': '此卡密不是本程序的卡密'
                })

            # 验证卡密（包含自动激活功能）
            result, message_or_account, 卡密信息 = self.付费管理器.检查卡密有效性(
                data['key'], data['machine_code']
            )

            if result:
                # 转换卡密信息为可JSON序列化的格式
                可序列化信息 = {}
                if 卡密信息:
                    for key, value in 卡密信息.items():
                        if isinstance(value, datetime.datetime):
                            可序列化信息[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            可序列化信息[key] = value

                # 处理返回的账号信息
                账号信息 = None
                消息 = "卡密验证成功"
                
                # 如果返回的是字典，则直接使用作为账号信息
                if isinstance(message_or_account, dict) and "邮箱" in message_or_account:
                    账号信息 = message_or_account
                    logging.info(f"卡密验证成功: {data['key']}, 邮箱: {账号信息['邮箱']}")
                # 否则使用返回的消息
                else:
                    消息 = message_or_account
                    logging.info(f"卡密验证成功但未获取账号: {data['key']}, 消息: {消息}")

                return jsonify({
                    'success': True,
                    'message': 消息,
                    'key_info': 可序列化信息,
                    'account_info': 账号信息
                })
            else:
                logging.info(f"卡密验证失败: {data['key']}, 原因: {message_or_account}")
                return jsonify({
                    'success': False,
                    'message': message_or_account
                })
        except Exception as e:
            logging.error(f"验证付费账号API错误: {e}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 卡密激活路由  适用于未激活的卡密
    def activate_key(self):
        """
        卡密激活API端点
        请求参数:
        - machine_code: 机器码（必填）
        - app_name: 程序名称（必填）
        - key_type: 卡密类型（必填，如"时卡"、"天卡"、"周卡"、"月卡"、"年卡"）
        - remark: 备注（可选，默认为"官方版"）
        返回:
        - success: 是否成功
        - message: 消息
        - key: 生成的卡密
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'machine_code' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少机器码参数'
                }), 400

            if 'app_name' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少程序名称参数'
                }), 400

            if 'key_type' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密类型参数'
                }), 400

            # 获取备注参数（可选）
            备注 = data.get('remark', '官方版')

            # 检查系统配置
            _, 开启付费号池 = self.付费管理器.检查系统配置()
            if 开启付费号池 == 0:
                return jsonify({
                    'success': False,
                    'message': '当前付费号池资源紧张,请稍后再试'
                })

            # 调用卡密激活方法
            result, message, 卡密 = self.付费管理器.卡密激活(
                data['machine_code'], data['app_name'], data['key_type'], 备注
            )

            if result:
                return jsonify({
                    'success': True,
                    'message': message,
                    'key': 卡密
                })
            else:
                return jsonify({
                    'success': False,
                    'message': message
                })
        except Exception as e:
            logging.error(f"API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 添加启动检查方法 智能判断新卡还是旧卡
    def check_startup(self):
        """
        启动检查API端点，智能判断卡密是否已激活，未激活时自动执行激活
        请求参数:
        - key: 卡密 (可选)
        - app_name: 程序名称
        - machine_code: 机器码（如果需要激活则必填）
        - remark: 备注（必填参数）
        返回:
        - success: 是否成功
        - message: 消息
        - expiry_time: 到期时间
        - enable_purchase: 是否启用购买功能（默认为1，仅当在代理秘钥表中找到且值不为1时才改变）
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400

            # 验证必填参数
            if 'app_name' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少程序名称参数'
                })
                
            if 'remark' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少备注参数'
                })

            程序名 = data['app_name']
            备注 = data['remark']
            机器码 = data.get('machine_code')
            卡密 = data.get('key')
            
            # 检查系统配置
            _, 开启付费号池, _, _ = self.系统配置.检查系统配置()
            系统配置 = {'开启付费号池': 开启付费号池}
            
            # 调用通用检查方法
            result, message, 到期时间, enable_purchase = self.付费管理器.check_startup(
                程序名, 备注, 机器码, 卡密, 系统配置
            )
            
            if result:
                response_data = {
                    'success': True,
                    'message': message,
                    'enable_purchase': enable_purchase
                }
                
                # 如果有到期时间，添加到响应中
                if 到期时间:
                    response_data['expiry_time'] = 到期时间.strftime("%Y-%m-%d %H:%M:%S") if isinstance(到期时间, datetime.datetime) else 到期时间
                
                return jsonify(response_data)
            else:
                return jsonify({
                    'success': False,
                    'message': message
                })
                
        except Exception as e:
            logging.error(f"启动检查API错误: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    # 添加清空机器码端点
    def clear_machine_code(self):
        """
        清空卡密对应的机器码
        请求参数:
        - key: 卡密
        返回:
        - success: 是否成功
        - message: 消息
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '缺少请求数据'
                }), 400
                
            # 验证必填参数
            if 'key' not in data:
                return jsonify({
                    'success': False,
                    'message': '缺少卡密参数'
                }), 400
                
            # 调用清空机器码方法
            result, message = self.付费管理器.清空机器码(data['key'])
            
            return jsonify({
                'success': result,
                'message': message
            })
            
        except Exception as e:
            logging.error(f"清空机器码API错误: {e}")
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    def 运行(self, host=None, port=None, debug=None):  # 生产环境中关闭debug模式
        """启动API服务"""
        # 使用配置值或默认值
        host = host or Config.API服务配置['监听地址']
        port = port or Config.API服务配置['端口']
        debug = debug if debug is not None else Config.API服务配置['调试模式']
        
        self.app.run(host=host, port=port, debug=debug)

# 启动应用
if __name__ == '__main__':
    服务 = API服务()
    服务.运行()